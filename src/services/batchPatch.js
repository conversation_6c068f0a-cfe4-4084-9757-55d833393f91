/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { transformWaybillType } from '@/components/_pages/Allocation/batchpatch/_utils/useOrderUpload';
import { isEmpty } from 'lodash';
import request from '../utils/request';
import { getCourierName } from '@/components/_pages/Allocation/batchpatch/_utils/usePatchForm';

export async function getSignerList(params) {
  return request('/v1/TotalDistribution/uploadGp/getSignerList', {
    method: 'POST',
    body: params,
  }).then(({ data }) => {
    const { custom } = data;
    return {
      data: (Array.isArray(custom) ? custom : []).map(item => ({
        value: item.signType,
        label: item.signType,
      })),
    };
  });
}

export async function getQuestionList(params) {
  return request('/v1/TotalDistribution/uploadGp/getBadWaybillType', {
    method: 'POST',
    body: params,
  }).then(({ data }) => {
    const isChildren = ['jt', 'yt'].includes(params.brand);
    const firstLevel = data
      .filter(i => (isChildren ? `${i.badWayBillType}` === '0' : true))
      .map(i => {
        if (isChildren) {
          i.children = data.filter(o => o.badWayBillType == i.badWayBillCode).map(o => ({
            value: o.badWayBillCode,
            label: o.badWayBillDesc,
          }));
        }
        return {
          ...i,
          value: i.badWayBillCode,
          label: i.badWayBillDesc,
        };
      });
    return {
      data: firstLevel,
    };
  });
}

export async function checkWaybillBrand(list, allCourierNameMap) {
  const _waybillType = list && list[0] ? list[0].waybillType : '';
  const formatParams = list.map(item => {
    const {
      waybill,
      courier_phone,
      isOperator,
      operatorCourier,
      new_dispatch_courier_phone,
    } = item;
    const isCheck = !!(isOperator && operatorCourier);
    return {
      waybillNo: waybill,
      dispatch_courier_phone: new_dispatch_courier_phone,
      phone: isCheck ? operatorCourier : courier_phone,
      check: isCheck ? '1' : '0',
    };
  });

  return request('/v1/TotalDistribution/uploadGp/batchCheckWaybillListBrand', {
    method: 'POST',
    body: {
      waybillType: transformWaybillType[_waybillType] || '',
      data_list: JSON.stringify(formatParams),
    },
  }).then(({ data = {} }) => {
    const formatResult = list.map(item => {
      const {
        waybill,
        courier_phone,
        courier_name,
        waybillType,
        operatorCourier,
        isOperator,
        OriginBrand,
        vehicle_no,
        new_dispatch_courier_phone,
        ...rest
      } = item;
      const { brand, name, dispatched_at } = data[waybill] || {};
      let msg = '';
      if (isOperator && !name) {
        msg = '未查找到派件员';
      } else if (brand) {
        if (brand == 'yt' && ['1', '5'].includes(waybillType) && !vehicle_no) {
          msg = '缺少车签号';
        }
        if (OriginBrand && OriginBrand != brand) {
          msg = '品牌不符';
        }
        if (waybillType == '17') {
          if (brand != 'yt') {
            msg = '不支持品牌';
          } else if (!courier_phone) {
            msg = '无扫描员信息';
          } else if (!new_dispatch_courier_phone) {
            msg = '无新派件员信息';
          } else if (!getCourierName(allCourierNameMap, courier_phone)) {
            msg = '扫描员未配置圆通工号';
          } else if (!getCourierName(allCourierNameMap, new_dispatch_courier_phone)) {
            msg = '新派件员未配置圆通工号';
          }
        }
      } else {
        msg = '未识别单号';
      }

      return {
        waybill,
        brand,
        courier_phone,
        courier_name,
        msg,
        waybillType,
        operatorCourier: operatorCourier || courier_phone,
        operatorCourier_name: isOperator ? name || courier_name : courier_name,
        new_dispatch_courier_phone,
        dispatched_at,
        ...rest,
      };
    });
    return formatResult;
  });
}

export async function getNoticePatch(list) {
  // 格式化数据
  const formatList = list.map(item => {
    const { brand, waybill, courier_phone, waybillType } = item;
    return {
      courier_phone,
      brand,
      waybillType: transformWaybillType[waybillType],
      waybillNo: waybill,
    };
  });

  return request('/v1/TotalDistribution/uploadGp/batchGetWaybillNoNoticeList', {
    method: 'POST',
    body: {
      data_list: JSON.stringify(formatList),
    },
  }).then(({ code, data }) => {
    if (`${code}` === '0') {
      // 处理返回
      const formatResult = list.map(item => {
        const { code: code_, data: data_ } = data[item.waybill] || {};
        let desc = '';
        if (`${code_}` === '0') {
          const { notice = [] } = data_ || {};
          desc = Array.isArray(notice) ? notice.map(i => i.desc).filter(i => !!i) : [];
        }
        return {
          waybill: item.waybill,
          desc,
        };
      });
      return formatResult;
    }

    return [];
  });
}

export async function uploadGp(list) {
  const formatParams = list.map(item => {
    const {
      waybillType,
      waybill,
      courier_phone,
      task_no,
      scan_time,
      brand,
      operatorCourier,
      signType,
      badWayBillDesc,
      badWayBillCode,
      vehicle_no,
      line_no,
      line_name,
      next_station_code,
      next_station_name,
      voucher_no,
      autoline_dispatch = false,
      need_arrive = true,
      need_dispatch = true,
      redispatch_type,
      new_dispatch_courier_phone,
    } = item;
    return {
      waybillNo: waybill,
      waybillType: transformWaybillType[waybillType], // 接口类型给的晚，按照自己定义类型开发，提交时按接口类型转换
      courier_phone,
      task_no,
      scan_time,
      brand,
      operatorCourier: waybillType == '17' ? new_dispatch_courier_phone : operatorCourier,
      signType,
      badWayBillDesc,
      badWayBillCode,
      vehicle_no,
      line_no,
      line_name,
      next_station_code,
      next_station_name,
      voucher_no,
      autoline_dispatch: autoline_dispatch ? 1 : 0,
      need_arrive: need_arrive ? 1 : 0,
      need_dispatch: need_dispatch ? 1 : 0,
      redispatch_to_me: redispatch_type,
    };
  });

  return request('/v1/TotalDistribution/uploadGp/batchUploadGp', {
    method: 'POST',
    body: {
      data_list: JSON.stringify(formatParams),
    },
  }).then(({ code, data }) => {
    if (`${code}` === '0') {
      const formatResult = list.map(item => {
        const { code: code_, msg } = data[item.waybill];
        return {
          waybill: item.waybill,
          errMsg: `${code_}` === '0' ? '' : msg,
        };
      });
      return formatResult;
    }
    return [];
  });
}

export async function exportFailOrders(params) {
  return request('/v1/TotalDistribution/uploadGp/export', {
    method: 'POST',
    body: {
      data: JSON.stringify(params),
    },
  }).then(({ code, data }) => (`${code}` === '0' && data?.filePath ? data?.filePath : ''));
}

export async function getLineList(params) {
  return request('/v1/TotalDistribution/uploadGp/getLineList', {
    method: 'POST',
    body: params,
  }).then(({ data }) => {
    const list = Array.isArray(data) ? data : [];
    return list.map(item => ({
      label: item.lineName,
      value: item.lineNo,
    }));
  });
}

export async function getNextStationList(params) {
  return request('/v1/TotalDistribution/uploadGp/getNextStationList', {
    method: 'POST',
    body: params,
  }).then(({ data }) => {
    const list = Array.isArray(data) ? data : [];
    return list.map(item => ({
      label: item.station_name,
      value: item.station_code,
    }));
  });
}

export async function getAutolineStatus(params) {
  return request('/v1/TotalDistribution/uploadGp/getAutolineStatus', {
    method: 'POST',
    body: params,
  }).then(({ code, data }) => {
    return code == 0 && data?.status == 1 ? true : false;
  });
}

export async function getYdManagerInfoList(params) {
  return request('/Api/Automation/SortLine/getYdManagerInfoList', {
    method: 'POST',
    body: params,
  });
}

export async function getZtManagerInfoList(params) {
  return request('/Api/Automation/SortingZtPdaScanner/getInfoList', {
    method: 'POST',
    body: params,
  });
}

export async function getStoManagerInfoList(params) {
  return request('/Api/Automation/SortingStoXzScanner/getInfoList', {
    method: 'POST',
    body: params,
  });
}

export async function getAutolineFcfConfig() {
  return new Promise((resolve, reject) => {
    Promise.all([getYdManagerInfoList(), getZtManagerInfoList(), getStoManagerInfoList()])
      .then(([yd = {}, zt = {}, sto = {}]) => {
        resolve({
          yd: yd.code == 0 && !isEmpty(yd.data) ? 1 : 0,
          zt: zt.code == 0 && !isEmpty(zt.data) ? 1 : 0,
          sto: sto.code == 0 && !isEmpty(sto.data) ? 1 : 0,
        });
      })
      .catch(reject);
  });
}

export async function getBrandsCanQuerySegmentCodes(params) {
  return request('/v1/TotalDistribution/uploadGp/getBrandsCanQuerySegmentCodes', {
    method: 'POST',
    body: params,
  });
}

export async function getAutolineConfig({ cm_phone } = {}) {
  return new Promise((resolve, reject) => {
    Promise.all([getAutolineFcfConfig(), getBrandsCanQuerySegmentCodes({ cm_phone })])
      .then(([fcf = {}, brandsCanQuerySegmentCodes = {}]) => {
        resolve({
          fcf,
          brandsCanQuerySegmentCodes: brandsCanQuerySegmentCodes?.data || {},
        });
      })
      .catch(reject);
  });
}

export async function getDispatchAutoArriveStatus(params) {
  return request('/v1/TotalDistribution/uploadGp/getDispatchAutoArriveStatus', {
    method: 'POST',
    body: params,
  }).then(({ code, data }) => {
    return code == 0 && data?.status == 1 ? true : false;
  });
}

export async function getDispatchAutoArriveConfig(params) {
  return request('/v1/TotalDistribution/uploadGp/getDispatchAutoArriveConfig', {
    method: 'POST',
    body: params,
  });
}

export async function setDispatchAutoArriveConfig(params) {
  return request('/v1/TotalDistribution/uploadGp/setDispatchAutoArriveConfig', {
    method: 'POST',
    body: params,
  });
}

export async function getSignAutoDispatchConfig(params) {
  return request('/v1/TotalDistribution/uploadGp/getSignAutoDispatchConfig', {
    method: 'POST',
    body: params,
  });
}

export async function setSignAutoDispatchConfig(params) {
  return request('/v1/TotalDistribution/uploadGp/setSignAutoDispatchConfig', {
    method: 'POST',
    body: params,
  });
}

export async function getAllCourierNameMap(params) {
  return request('/v1/TotalDistribution/uploadGp/getAllCourierNameMap', {
    method: 'POST',
    body: params,
  }).then(({ code, data }) => {
    return code == 0 && data ? data : {};
  });
}

export async function getSpecialConfig(params) {
  return request('/v1/TotalDistribution/GunInformation/getOptions', {
    method: 'POST',
    body: params,
  }).then(({ code, data }) => {
    return code == 0 && data ? data : {};
  });
}

export async function getSpecialConfigOne(params) {
  return request('/v1/TotalDistribution/GunInformation/getOption', {
    method: 'POST',
    body: params,
  }).then(({ code, data }) => {
    return code == 0 && data ? data : {};
  });
}

export async function setSpecialConfig(params) {
  return request('/v1/TotalDistribution/GunInformation/setOption', {
    method: 'POST',
    body: params,
  });
}
