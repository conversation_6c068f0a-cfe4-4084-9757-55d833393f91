/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useSetState, useBoolean } from 'ahooks';

// 到件 5 派件 3 签收 2 问题件 4

export const patchScanType = [
  {
    label: '到件',
    dataKey: 'daojian',
    value: '1',
    waybillType: '5',
    specialConfigType: '1',
  },
  {
    label: '派件',
    dataKey: 'paijian',
    value: '2',
    waybillType: '3',
    specialConfigType: '2',
  },
  {
    label: '签收',
    dataKey: 'qianshou',
    value: '3',
    waybillType: '2',
  },
  {
    label: '问题件',
    dataKey: 'wentijian',
    value: '4',
    waybillType: '4',
  },
  {
    label: '到派',
    dataKey: 'daopai',
    value: '5',
    waybillType: '9',
    specialConfigType: '3',
  },
  {
    label: '发件',
    dataKey: 'fajian',
    value: '6',
    waybillType: '6',
  },
  {
    label: '改派',
    dataKey: 'gaipai',
    value: '17',
    waybillType: '17',
  },
];

export function useBatchPatchIndex() {
  const [spinning, { toggle: toggleLoading }] = useBoolean(false);

  const [pageData, setPageData] = useSetState({
    // 扫描类型
    scanLabel: patchScanType[0].label,
    scanDataKey: patchScanType[0].dataKey, // 缓存列表时的key
    scanValue: patchScanType[0].value,

    // 扫描员列表
    operateList: [],
    // 扫描员信息/改派-新派件员信息
    courier_phone: '',
    courier_name: '',
    courier_no: '',
    // 改派-新派件员信息
    new_dispatch_courier_phone: '',
    new_dispatch_courier_name: '',
    new_dispatch_courier_no: '',
    // 签收类型
    signType: '',
    // 问题类型
    question_id: '',
    question_label: '',

    // 特殊件检查
    TE_SHU_JIAN_JIAN_CHA: true,

    // 特殊配置
    specialConfig: [],
  });

  return {
    pageData,
    updatePageData: setPageData,
    spinning,
    toggleLoading,
  };
}
