/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useDebounceEffect, useUpdateEffect } from 'ahooks';
import { Icon, Modal, Row, Tag, message } from 'antd';
import React, { useState, useMemo, useEffect } from 'react';
import moment from 'moment';
import { MAX_ORDER_LENGTH, useOrderUpload, ASYNC_UPLOAD_FILES_NUM } from './useOrderUpload';
import { transformBrand } from './transformBrand';
import { useIndexedDBState } from './useIndexedDBState';
import { stubFalse } from 'lodash';

export function usePatchTable(props) {
  const { pageData, toggleLoading, updatePageData } = props;
  const { scanDataKey, scanLabel, scanValue, list, addListStatus, FA_JIAN_BRAND } = pageData;

  const [selectRows, setSelectRows] = useState([]);
  const [localData = [], setLocalData] = useIndexedDBState(scanDataKey);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: ASYNC_UPLOAD_FILES_NUM,
  });

  const { loading, getStatus, uploadOrders } = useOrderUpload({ toggleLoading });

  // IndexedDB 中已经过滤了2小时内的数据，这里直接使用
  const formatLocalData = localData;

  const isShowErrMsg = useMemo(() => formatLocalData.some(i => !!i.errMsg), [formatLocalData]);

  const columns = useMemo(
    () =>
      [
        {
          title: '单号',
          dataIndex: 'waybill',
          key: 'waybill',
          render: (val, item) => (
            <Row align="middle" justify="center">
              <div>{val}</div>
              {!item.init && <Icon type="loading" style={{ color: '#1890FF' }} />}
              {!!(item?.desc && item?.desc?.length) &&
                item.desc.map(i => (
                  <Tag key={i} color="red">
                    {i}
                  </Tag>
                ))}
            </Row>
          ),
        },
        {
          title: '品牌',
          dataIndex: 'brand',
          key: 'brand',
          render: val => (val == 'ems' ? 'EMS' : transformBrand(val)),
        },
        {
          title: '扫描员',
          dataIndex: 'name',
          key: 'name',
          render: (_, item) => `${item.courier_name || ''} ${item.courier_phone || ''}`,
        },
        {
          title: '上次派件时间',
          dataIndex: 'dispatched_at',
          key: 'dispatched_at',
          hide: scanValue != '17',
        },
        {
          title: '新派件员',
          dataIndex: 'name',
          key: 'name',
          render: (_, item) =>
            `${item.new_dispatch_courier_name || ''} ${item.new_dispatch_courier_phone || ''}`,
          hide: scanValue != '17',
        },
        {
          title: '派件员',
          dataIndex: 'phone',
          key: 'phone',
          hide: !['2', '5'].includes(scanValue),
          render: (_, item) => `${item.operatorCourier_name || ''} ${item.operatorCourier || ''}`,
        },
        {
          title: '签收类型',
          dataIndex: 'signType',
          key: 'signType',
          hide: scanValue != '3',
        },
        {
          title: '问题类型',
          dataIndex: 'badWayBillDesc',
          key: 'badWayBillDesc',
          hide: scanValue != '4',
        },
        {
          title: '下一站',
          dataIndex: 'next_station_name',
          key: 'next_station_name',
          hide: scanValue != '6',
        },
        {
          title: '车签号',
          dataIndex: 'vehicle_no',
          key: 'vehicle_no',
          hide: !(scanValue == '6' && FA_JIAN_BRAND == 'yt'),
        },
        {
          title: '线路',
          dataIndex: 'line_name',
          key: 'line_name',
          hide: !(scanValue == '6' && FA_JIAN_BRAND == 'yt'),
        },
        {
          title: '发车凭证',
          dataIndex: 'voucher_no',
          key: 'voucher_no',
          hide: !(scanValue == '6' && FA_JIAN_BRAND == 'yd'),
        },
        {
          title: '扫描时间',
          dataIndex: 'scan_time',
          key: 'scan_time',
        },
        {
          title: '失败原因',
          dataIndex: 'errMsg',
          key: 'errMsg',
          hide: !isShowErrMsg,
          render: val => <span style={{ color: 'red' }}>{val}</span>,
        },
      ].filter(item => !item.hide),
    [scanValue, isShowErrMsg, FA_JIAN_BRAND],
  );

  // 上传
  const onUpload = async () => {
    if (loading) return;
    const uploadList = formatLocalData.filter(i => selectRows.includes(i.waybill));
    const result = await uploadOrders(uploadList);

    // 处理点选列表。
    const forSelectRow = result.filter(i => i.errMsg).map(i => i.waybill);
    setSelectRows([...forSelectRow]);
    // 处理提交的数据，提交成功的删掉，提交失败的同步失败原因
    const forLocalData = formatLocalData
      .map(i => {
        const item = result.find(o => o.waybill == i.waybill);
        if (item) {
          if (item.errMsg) {
            return {
              ...i,
              ...item,
            };
          }
          return '';
        }
        return i;
      })
      .filter(i => !!i);

    setLocalData([...forLocalData]);

    // 上传后检查当前页是否还有数据，如果没有则跳转到有数据的页面
    setTimeout(() => {
      const newTotalPages = Math.ceil(forLocalData.length / pagination.pageSize);
      if (pagination.current > newTotalPages && newTotalPages > 0) {
        setPagination(prev => ({
          ...prev,
          current: newTotalPages,
        }));
      }
    }, 100);
  };

  // 删除
  const onDelete = () => {
    Modal.confirm({
      centered: true,
      title: '提示',
      content: '确定删除？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        const format = formatLocalData.filter(i => !selectRows.includes(i.waybill));
        setLocalData(format);
        setSelectRows([]);
        message.success('删除成功');

        // 删除后检查当前页是否还有数据，如果没有则跳转到有数据的页面
        setTimeout(() => {
          const newTotalPages = Math.ceil(format.length / pagination.pageSize);
          if (pagination.current > newTotalPages && newTotalPages > 0) {
            setPagination(prev => ({
              ...prev,
              current: newTotalPages,
            }));
          }
        }, 100);
      },
    });
  };

  // 添加新单号
  useUpdateEffect(
    () => {
      // 去重
      const formatList = list
        .filter(i => !formatLocalData.some(o => o.waybill == i.waybill))
        .map(i => ({
          ...i,
          init: false,
        }));

      if (formatList.length > 0) {
        const localList = [...formatList, ...formatLocalData];
        setLocalData(localList);
        getOrderStatus(localList);
      }
    },
    [addListStatus],
  );

  // 获取单号状态方法
  const getOrderStatus = async lis => {
    console.info('getOrderStatus====>lis', lis);
    const list_ = lis.filter(i => {
      if (i.TE_SHU_JIAN_JIAN_CHA != undefined) {
        return i.TE_SHU_JIAN_JIAN_CHA !== '0';
      }
      return !i.init;
    });

    console.info('getOrderStatus====>filter', list_);

    const orders = list_.length ? await getStatus(list_) : [];

    const formatList = lis
      .map(i => {
        const obj = orders.find(o => o.waybill == i.waybill) || {};
        return {
          ...i,
          ...obj,
          init: true,
        };
      })
      .filter(item => {
        const { scan_time } = item;
        const isAfter = moment().diff(moment(scan_time), 'hours') < 2;
        return isAfter;
      });

    // 只有当数据真正发生变化时才更新
    const hasChanges = formatList.some((item, index) => {
      const originalItem = lis[index];
      return (
        !originalItem ||
        originalItem.init !== item.init ||
        JSON.stringify(originalItem) !== JSON.stringify(item)
      );
    });

    if (hasChanges) {
      setLocalData([...formatList]);
    }
  };

  useEffect(
    () => {
      console.log('localData====>', localData);
    },
    [localData],
  );

  // 切换类型后，清空选择项并重置处理状态
  useUpdateEffect(
    () => {
      setSelectRows([]);
      // 重置分页状态
      setPagination({
        current: 1,
        pageSize: ASYNC_UPLOAD_FILES_NUM,
      });
    },
    [scanDataKey],
  );

  // 当数据总量发生显著变化时，智能调整分页
  useUpdateEffect(
    () => {
      // 计算总页数
      const totalItems = formatLocalData.length;
      const { current, pageSize } = pagination;
      const totalPages = Math.ceil(totalItems / pageSize);

      // 只有在以下情况才重置页码：
      // 1. 当前页码超过了总页数（数据减少导致当前页不存在）
      if (current > totalPages && totalPages > 0) {
        // 重置到最后一页，而不是第一页
        setPagination(prev => ({
          ...prev,
          current: totalPages,
        }));
      }
      // 注意：当数据增加时，我们保持用户在当前页面
    },
    [
      Math.ceil(formatLocalData.length / pagination.pageSize),
      pagination.current,
      pagination.pageSize,
    ],
  );

  useDebounceEffect(
    () => {
      if (scanDataKey == 'fajian') {
        const brand = formatLocalData[0]?.brand || '';
        if ((brand || FA_JIAN_BRAND) && brand != FA_JIAN_BRAND) {
          updatePageData({
            FA_JIAN_BRAND: brand,
          });
        }
      } else {
        updatePageData({
          FA_JIAN_BRAND: '',
        });
      }
    },
    [scanDataKey, formatLocalData.length, FA_JIAN_BRAND],
    { wait: 300 },
  );

  const onCheckedAll = event => {
    const { checked } = event.target;
    if (checked) {
      const checkedList = formatLocalData.map(i => i.waybill);
      if (checkedList.length > MAX_ORDER_LENGTH) {
        setSelectRows(checkedList.slice(0, MAX_ORDER_LENGTH));
      } else {
        setSelectRows(checkedList);
      }
    } else {
      setSelectRows([]);
    }
  };

  // 处理表格分页变化
  const handleTableChange = paginationParams => {
    setPagination(paginationParams);
    // 分页变化时不需要清空选中状态，保持跨页选择功能
  };

  // 完善的分页配置，包含total等必要属性
  const paginationConfig = useMemo(
    () => ({
      ...pagination,
      total: formatLocalData.length,
      showSizeChanger: false,
      showQuickJumper: stubFalse,
      showTotal: (total) => `共 ${total} 条`,
    }),
    [pagination, formatLocalData.length],
  );

  // 获取当前页的数据
  const getPaginatedData = useMemo(
    () => {
      const { current, pageSize } = pagination;
      const startIndex = (current - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      return formatLocalData.slice(startIndex, endIndex);
    },
    [formatLocalData, pagination],
  );

  return {
    tableTitle: scanLabel,
    selectRows,
    setSelectRows,
    localData: formatLocalData,
    paginatedData: getPaginatedData,
    pagination: paginationConfig,
    onTableChange: handleTableChange,
    onUpload,
    onDelete,
    columns,
    loading,
    onCheckedAll,
  };
}
