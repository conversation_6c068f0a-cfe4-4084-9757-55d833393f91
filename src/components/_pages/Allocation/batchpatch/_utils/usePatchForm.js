/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import moment from 'moment';
import { Modal, message } from 'antd';
import { random } from 'lodash';
import { useEffect, useMemo } from 'react';
import { useRequest, useUpdateEffect } from 'ahooks';
import {
  getAllCourierNameMap,
  getAutolineStatus,
  getDispatchAutoArriveConfig,
  getSignAutoDispatchConfig,
  getSpecialConfig,
} from '@/services/batchPatch';
import { sliceArrByNum } from '@/utils/utils';
import { useMultipleGetBrand } from './useGetBrand';
import { MAX_ORDER_LENGTH } from './useOrderUpload';
import { patchScanType } from './useBatchPatchIndex';

export function modalAlert(props) {
  return new Promise((resolve, reject) => {
    const { content, hideCancel, ...rest } = props;
    Modal.confirm({
      centered: true,
      content,
      okText: '确定',
      cancelText: '取消',
      cancelButtonProps: {
        style: hideCancel ? { display: 'none' } : {},
      },
      ...rest,
      onOk: () => {
        resolve(true);
      },
      onCancel: () => {
        reject();
      },
    });
  });
}
// 特殊符号转换
const transformOrders = orders => {
  const str = orders
    .replace(/\n/g, ',')
    .replace(/，/g, ',')
    .replace(/；/g, ',')
    .replace(/;/g, ',');
  const arr = [...new Set(str.split(',').filter(i => i))];
  return arr;
};

export function usePatchForm(props) {
  const { form, pageData, updatePageData, toggleLoading } = props;
  const { validateFields, getFieldDecorator, getFieldsValue, setFieldsValue } = form || {};
  const {
    courier_phone,
    courier_name,
    courier_no,
    new_dispatch_courier_phone,
    new_dispatch_courier_name,
    new_dispatch_courier_no,
    scanValue,
    signType,
    question_id,
    question_label,
    brand,
    FA_JIAN_BRAND = '', // 已导入的发件列表中的品牌，发件状态下，且存在此品牌，表单中品牌锁定，不可切换
  } = pageData;
  const isGaipai = !!(scanValue == '17'); // 改派

  const { data: allCourierNameMap = {} } = useRequest(getAllCourierNameMap);
  const { data: autolineStatus = false, run: runAutolineStatus } = useRequest(getAutolineStatus, {
    manual: true,
  });
  const { data: dispatchAutoArriveStatus, run: runDispatchAutoArriveStatus } = useRequest(
    getDispatchAutoArriveConfig,
    {
      manual: true,
    },
  );
  const { data: signAutoDispatchStatus, run: runSignAutoDispatchStatus } = useRequest(
    getSignAutoDispatchConfig,
    {
      manual: true,
    },
  );

  const { data: specialCheckConfig, run: runSpecialCheckConfig } = useRequest(
    () => getSpecialConfig({ types: [1, 2, 3] }),
    {
      manual: true,
    },
  );

  const { redispatch_type = '0' } = getFieldsValue ? getFieldsValue() : {};

  useEffect(() => {
    runSpecialCheckConfig();
  }, []);

  useEffect(
    () => {
      if (specialCheckConfig) {
        updatePageData({
          TE_SHU_JIAN_JIAN_CHA:
            specialCheckConfig.find(
              item => item.type == patchScanType.find(i => i.value == scanValue)?.specialConfigType,
            )?.status == 1
              ? false
              : true,
          specialConfig: specialCheckConfig,
        });
      }
    },
    [specialCheckConfig, scanValue],
  );

  useEffect(
    () => {
      if (['2', '5'].includes(`${scanValue}`)) {
        runAutolineStatus();
      }
    },
    [scanValue],
  );

  useEffect(
    () => {
      if (['2', '3'].includes(`${scanValue}`) && courier_phone) {
        runDispatchAutoArriveStatus({ cm_phone: courier_phone });
      }
      if (['3'].includes(`${scanValue}`) && courier_phone) {
        runSignAutoDispatchStatus({ cm_phone: courier_phone });
      }
    },
    [scanValue, courier_phone],
  );

  useUpdateEffect(
    () => {
      if (['17'].includes(`${scanValue}`) && redispatch_type == '1') {
        setFieldsValue({
          new_dispatch_courier: courier_phone || undefined,
        });
        updatePageData({
          new_dispatch_courier_phone: courier_phone,
          new_dispatch_courier_name: courier_name,
          new_dispatch_courier_no: courier_no,
        });
      }
    },
    [scanValue, redispatch_type, courier_phone, courier_name, courier_no],
  );

  const { loading, getOrderBrands } = useMultipleGetBrand({
    toggleLoading,
    getFieldsValue,
    pageData,
  });

  const checkedDataFormat = res => {
    const {
      task_no,
      vehicle_no,
      voucher_no,
      nextStation = {},
      lineInfo = {},
      autoline_dispatch = false,
      need_arrive = true,
      need_dispatch = true,
    } = getFieldsValue();

    /**
     * 分割批次，以1000为基数分割数组，分割后当前数组中的上传时间为一个序列。
     * 1/1001/2001/3001以此类推 的上传时间为相同时间
     */
    const list = sliceArrByNum(res, 1000);
    const dateNow = Date.now();
    return list
      .map(item =>
        item.map((i, index) => ({
          ...i,
          id: i.waybill,
          task_no,
          vehicle_no,
          voucher_no,
          signType,
          badWayBillDesc: question_label,
          badWayBillCode: question_id,
          waybillType: scanValue,
          scan_time: moment(dateNow)
            .subtract(index + random(0, 1000), 'milliseconds')
            .format('YYYY-MM-DD HH:mm:ss'),
          TE_SHU_JIAN_JIAN_CHA: pageData.TE_SHU_JIAN_JIAN_CHA ? '1' : '0', // 特殊件检查
          next_station_code: nextStation.key || '',
          next_station_name: nextStation.label || '',
          line_no: lineInfo.key || '',
          line_name: lineInfo.label || '',
          autoline_dispatch,
          need_arrive,
          redispatch_type,
          need_dispatch,
        })),
      )
      .flat();
  };

  const onCheckOrderBrand = orders => {
    getOrderBrands(orders, { allCourierNameMap }).then(res => {
      console.time('拼接数据时间=====>');
      const list = checkedDataFormat(res);
      console.timeEnd('拼接数据时间=====>', list);
      updatePageData({
        list,
        addListStatus: Date.now(),
        TE_SHU_JIAN_JIAN_CHA: pageData.specialConfig.find(
          item => item.type == patchScanType.find(i => i.value == scanValue)?.specialConfigType,
        )?.status == 1
          ? false
          : true,
      });
      setFieldsValue({
        orders: '',
        TE_SHU_JIAN_JIAN_CHA: pageData.specialConfig.find(
          item => item.type == patchScanType.find(i => i.value == scanValue)?.specialConfigType,
        )?.status == 1
          ? false
          : true,
        autoline_dispatch: false,
        need_arrive: true,
        need_dispatch: true,
      });
    });
  };

  const handleSubmit = () => {
    if (loading) return;
    validateFields((err, val) => {
      if (!err) {
        console.log('val', val);
        const { orders, courier, vehicle_no, new_dispatch_courier } = val;
        if (!courier) {
          message.error('请选择扫描员');
          return;
        }
        if (isGaipai && !new_dispatch_courier) {
          message.error('请选择新派件员');
          return;
        }
        if (!orders) {
          message.error('请输入单号');
          return;
        }
        const formatOrders = transformOrders(orders);
        if (formatOrders.length > MAX_ORDER_LENGTH) {
          message.error(`最大单次只允许提交/导入${MAX_ORDER_LENGTH}个单号，已超过！`);
          return;
        }
        onCheckOrderBrand(
          formatOrders.map(i => ({
            waybill: i,
            courier_phone,
            courier_name,
            courier_no,
            waybillType: scanValue,
            vehicle_no,
            new_dispatch_courier_phone,
            new_dispatch_courier_name,
            new_dispatch_courier_no,
          })),
        );
      }
    });
  };

  // 表单类型的显示
  const formItemShow = useMemo(
    () => {
      const vehicle_no =
        ['1', '5'].includes(scanValue) || (['6'].includes(scanValue) && brand == 'yt');

      // 韵达发车凭证
      const voucher_no =
        ['1', '5'].includes(scanValue) || (['6'].includes(scanValue) && brand == 'yd');

      return {
        vehicle_no, // 圆通车签号
        voucher_no, // 韵达发车凭证 到付时使用
        task_no: ['1', '5'].includes(scanValue), // 极兔任务号
        TE_SHU_JIAN_JIAN_CHA: ['1', '2', '5'].includes(scanValue), // 特殊件检查
        brand: ['3', '4', '6', '17'].includes(scanValue), // 品牌
        disabled_brand: ['6'].includes(scanValue) && !!FA_JIAN_BRAND, // 发件状态下，且列表中存在品牌，锁定品牌
        signer: ['3'].includes(scanValue), // 签收人
        question: ['4'].includes(scanValue), // 问题类型
        nextStation: ['6'].includes(scanValue), // 下一站
        lineInfo: ['6'].includes(scanValue) && brand == 'yt', // 圆通线路
        autoline_dispatch: autolineStatus && ['2', '5'].includes(scanValue), // 按自动化配置段码补派件
        need_arrive: `${dispatchAutoArriveStatus?.code}` === '0' && ['2', '3'].includes(scanValue), // 派件自动补到件
        redispatch_type: ['17'].includes(scanValue), // 改派方式
        new_dispatch_courier: ['17'].includes(scanValue), // 改派-新派件员
        need_dispatch: `${signAutoDispatchStatus?.code}` === '0' && ['3'].includes(scanValue), // 签收自动补派件
      };
    },
    [
      scanValue,
      brand,
      FA_JIAN_BRAND,
      autolineStatus,
      dispatchAutoArriveStatus,
      signAutoDispatchStatus,
    ],
  );

  useUpdateEffect(
    () => {
      if (scanValue == '6' && FA_JIAN_BRAND) {
        form.setFieldsValue({
          brand: FA_JIAN_BRAND,
        });
      }
    },
    [FA_JIAN_BRAND],
  );

  const disabledBrandHelp = () => {
    Modal.info({
      content: '列表中已有导入品牌，请继续操作上传',
      centered: true,
      okText: '我知道了',
    });
  };

  return {
    form,
    getFieldDecorator,
    validateFields,
    loading,
    getFieldsValue,
    setFieldsValue,
    handleSubmit,
    onCheckOrderBrand,
    formItemShow,
    disabledBrandHelp,
    dispatchAutoArriveData: dispatchAutoArriveStatus?.data,
    signAutoDispatchData: signAutoDispatchStatus?.data,
    allCourierNameMap,
    specialCheckConfig,
    runSpecialCheckConfig,
  };
}

export const getCourierName = (map, phone) => {
  return (map && phone && map[phone]) || '';
};
