/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useEffect, useCallback, useMemo, useState } from 'react';
import { useLiveQuery } from 'dexie-react-hooks';
import moment from 'moment';
import { db } from './db';
import { autoMigrate } from './migrationHelper';

/**
 * 使用 IndexedDB 替代 localStorage 的自定义 hook
 * @param {string} scanType - 扫描类型，对应原来的 scanDataKey
 * @returns {[Array, Function, boolean]} - [数据, 设置数据的函数, loading状态]
 */
export function useIndexedDBState(scanType) {
  // IndexedDB 操作的 loading 状态
  const [isLoading, setIsLoading] = useState(false);

  // 首次使用时自动迁移数据
  useEffect(() => {
    autoMigrate();
  }, []);

  // 判断数据是否在有效期内（2小时内）
  const isDataValid = useCallback((scanTime) => {
    return moment().diff(moment(scanTime), 'hours') < 2;
  }, []);

  // 清除指定类型的过期数据
  const clearExpiredData = useCallback(async () => {
    if (!scanType) return;

    try {
      // 查询该scanType下的所有数据
      const allData = await db.scanData
        .where('scanType')
        .equals(scanType)
        .toArray();

      // 找出过期的数据ID列表
      const expiredIds = allData
        .filter(item => !isDataValid(item.scan_time))
        .map(item => [item.scanType, item.waybill]);

      // 如果有过期数据，执行批量删除
      if (expiredIds.length > 0) {
        await db.scanData.bulkDelete(expiredIds);
        console.log(`已清除 ${scanType} 类型的过期数据 ${expiredIds.length} 条`);
      }
    } catch (error) {
      console.error('清除过期数据失败:', error);
    }
  }, [scanType, isDataValid]);

  // 清除所有类型的过期数据
  const clearAllExpiredData = useCallback(async () => {
    try {
      // 查询所有数据
      const allData = await db.scanData.toArray();

      // 找出过期的数据ID列表
      const expiredIds = allData
        .filter(item => !isDataValid(item.scan_time))
        .map(item => [item.scanType, item.waybill]);

      // 如果有过期数据，执行批量删除
      if (expiredIds.length > 0) {
        await db.scanData.bulkDelete(expiredIds);
        console.log(`已清除全部类型的过期数据 ${expiredIds.length} 条`);
      }
    } catch (error) {
      console.error('清除过期数据失败:', error);
    }
  }, [isDataValid]);

  // 组件挂载时清除过期数据
  useEffect(() => {
    if (scanType) {
      clearExpiredData();
    }
  }, [scanType, clearExpiredData]);

  // 使用 dexie-react-hooks 的 useLiveQuery 实时查询数据
  const rawData = useLiveQuery(
    async () => {
      if (!scanType) return [];

      // 查询指定扫描类型的数据，不在这里过滤时间
      const result = await db.scanData
        .where('scanType')
        .equals(scanType)
        .toArray();

      return result;
    },
    [scanType],
  );

  // 使用 useMemo 来稳定时间过滤逻辑，避免频繁重新计算
  const data = useMemo(() => {
    if (!rawData || !Array.isArray(rawData)) return [];

    // 只返回2小时内的数据，使用isDataValid函数
    const filteredData = rawData.filter(item => isDataValid(item.scan_time));

    // 按运单号排序，确保数据顺序稳定
    return filteredData.sort((a, b) => (a.waybill || '').localeCompare(b.waybill || ''));
  }, [rawData, isDataValid]);

  // 设置数据的函数
  const setData = useCallback(
    async newData => {
      if (!scanType) return;

      // 开始 loading
      setIsLoading(true);

      try {
        // 使用事务确保删除和插入操作的原子性，避免中间状态导致的数据闪烁
        await db.transaction('rw', db.scanData, async () => {
          // 先清除该扫描类型的所有数据
          await db.scanData
            .where('scanType')
            .equals(scanType)
            .delete();

          // 添加新数据，为每个数据项添加 scanType 字段
          if (newData && newData.length > 0) {
            const dataWithScanType = newData.map(item => ({
              ...item,
              scanType,
            }));
            await db.scanData.bulkPut(dataWithScanType);
          }
        });

        // 设置新数据后，清除其他类型的过期数据
        await clearAllExpiredData();
      } catch (error) {
        console.error('IndexedDB 操作失败:', error);
      } finally {
        // 结束 loading
        setIsLoading(false);
      }
    },
    [scanType, clearAllExpiredData],
  );

  return [data || [], setData, isLoading];
}
