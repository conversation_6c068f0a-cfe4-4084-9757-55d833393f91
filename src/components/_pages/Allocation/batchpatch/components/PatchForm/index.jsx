/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Button, Card, Col, Form, Input, Row, Select } from 'antd';
import React from 'react';
import { InfoCircleOutlined } from '@ant-design/icons';
import PatchFormScan from './components/scan';
import PatchFormOperate from './components/operate';
import { usePatchForm } from '../../_utils/usePatchForm';
import PatchFormUpload from './components/upload';
import PatchFormTemplateModal from './components/templateModal';
import PatchFormBrand from './components/brand';
import PatchFormSigner from './components/signer';
import PatchFormQuestion from './components/question';

import styles from './index.less';
import { MAX_ORDER_LENGTH } from '../../_utils/useOrderUpload';
import PatchFormLine from './components/line';
import PatchFormNextStation from './components/nextStation';
import AutoConfigPatch from './components/autoConfigPatch';
import PatchD<PERSON>jian from './components/patchDaojian';
import Patch<PERSON>aijian from './components/patchPaijian';
import AuthorizedExtend from '@/components/Authorized/AuthorizedExtend';
import SpecialCheck from './components/specialCheck';

const { TextArea } = Input;
const { Item: FormItem } = Form;

const PatchForm = Form.create({
  onValuesChange: (props, value) => {
    console.log('onValuesChange===>', value);
    const {
      form: { getFieldsValue, setFieldsValue },
      updatePageData,
      pageData,
    } = props;
    const { brand, redispatch_type, ...rest } = getFieldsValue();
    const { scanValue } = pageData;

    console.info('getFieldsValue====>', rest);

    if (scanValue == '6') {
      if (value.hasOwnProperty('courier')) {
        setFieldsValue({
          nextStation: undefined,
          lineInfo: undefined,
        });
      }
      // 当前设置品牌变动，清空下一站信息
      if (value.hasOwnProperty('brand')) {
        if (brand != value.brand) {
          setFieldsValue({
            nextStation: undefined,
          });
        }
      }
    }

    if (value.hasOwnProperty('scan')) {
      updatePageData({
        brand,
        new_dispatch_courier_phone: '',
        new_dispatch_courier_name: '',
        new_dispatch_courier_no: '',
      });
    }
    if (value.hasOwnProperty('brand')) {
      updatePageData(value); // 切换品牌时，本地数据要存一份
    }
  },
})(props => {
  const { pageData, updatePageData } = props;
  // console.log('pageData===>', pageData);
  const { scanValue, courier_phone } = pageData;
  const {
    form,
    loading,
    getFieldDecorator,
    validateFields,
    getFieldsValue,
    setFieldsValue,
    handleSubmit,
    onCheckOrderBrand,
    formItemShow,
    disabledBrandHelp,
    dispatchAutoArriveData,
    signAutoDispatchData,
    allCourierNameMap,
    runSpecialCheckConfig,
  } = usePatchForm(props);
  const { redispatch_type = '0' } = getFieldsValue ? getFieldsValue() : {};

  const isGaipai = !!(scanValue == '17'); // 改派
  return (
    <Card>
      <Form form={form} labelAlign="left" className={styles.patchForm}>
        <FormItem label="扫描员">
          {getFieldDecorator('courier', {
            rules: [
              {
                required: true,
                message: '请选择扫描员',
              },
            ],
          })(<PatchFormOperate placeholder="请选择扫描员" onHandleSelect={updatePageData} />)}
        </FormItem>
        <FormItem label="扫描类型">
          {getFieldDecorator('scan', {
            initialValue: scanValue,
            rules: [
              {
                required: true,
              },
            ],
          })(<PatchFormScan onHandleSelect={updatePageData} />)}
        </FormItem>
        {formItemShow.brand && (
          <FormItem
            label="品牌"
            help={
              formItemShow.disabled_brand ? (
                <div style={{ cursor: 'pointer' }} onClick={disabledBrandHelp}>
                  品牌已锁定 <InfoCircleOutlined />
                </div>
              ) : (
                ''
              )
            }
          >
            {getFieldDecorator('brand', {
              initialValue: isGaipai ? 'yt' : undefined,
              rules: [
                {
                  required: true,
                  message: '请选择品牌',
                },
              ],
            })(
              <PatchFormBrand
                disabled={formItemShow.disabled_brand}
                scanValue={scanValue}
                setFieldsValue={setFieldsValue}
              />,
            )}
          </FormItem>
        )}
        {formItemShow.signer && (
          <FormItem label="签收人">
            {getFieldDecorator('signer', {
              rules: [
                {
                  required: true,
                  message: '签收人',
                },
              ],
            })(
              <PatchFormSigner
                getFieldsValue={getFieldsValue}
                pageData={pageData}
                onHandleSelect={updatePageData}
              />,
            )}
          </FormItem>
        )}
        {formItemShow.redispatch_type && (
          <FormItem label="改派方式">
            {getFieldDecorator('redispatch_type', {
              initialValue: isGaipai ? '0' : '',
              rules: [
                {
                  required: true,
                  message: '请选择改派方式',
                },
              ],
            })(
              <Select>
                <Select.Option value="0">改派给他</Select.Option>
                <Select.Option value="1">改派给我</Select.Option>
              </Select>,
            )}
          </FormItem>
        )}
        {formItemShow.new_dispatch_courier && (
          <FormItem label="新派件员">
            {getFieldDecorator('new_dispatch_courier', {
              rules: [
                {
                  required: true,
                  message: '请选择新派件员',
                },
              ],
            })(
              <PatchFormOperate
                disabled={redispatch_type == '1'}
                placeholder="请选择新派件员"
                onHandleSelect={({
                  courier_phone: _courier_phone,
                  courier_name: _courier_name,
                  courier_no: _courier_no,
                }) => {
                  if (_courier_phone) {
                    updatePageData({
                      new_dispatch_courier_phone: _courier_phone,
                      new_dispatch_courier_name: _courier_name,
                      new_dispatch_courier_no: _courier_no,
                    });
                  }
                }}
              />,
            )}
          </FormItem>
        )}
        {formItemShow.nextStation && (
          <FormItem label="下一站">
            {getFieldDecorator('nextStation', {
              rules: [
                {
                  required: true,
                  message: '请选择下一站',
                },
              ],
            })(<PatchFormNextStation getFieldsValue={getFieldsValue} />)}
          </FormItem>
        )}
        {formItemShow.vehicle_no && (
          <FormItem label="圆通车签号" name="vehicle_no">
            {getFieldDecorator('vehicle_no', {
              rules: [
                {
                  required: scanValue == '6',
                  message: '请输入圆通车签号',
                },
                {
                  pattern: /^[0-9a-zA-Z]+$/g,
                  message: '请输入正确的圆通车签号',
                },
              ],
            })(<Input maxLength={30} placeholder="请输入圆通车签号" />)}
          </FormItem>
        )}
        {formItemShow.voucher_no && (
          <FormItem label="韵达发车凭证" name="voucher_no">
            {getFieldDecorator('voucher_no', {
              rules: [
                {
                  pattern: /^[0-9a-zA-Z]+$/g,
                  message: '请输入正确的韵达发车凭证',
                },
              ],
            })(<Input maxLength={30} placeholder="请输入韵达发车凭证" />)}
          </FormItem>
        )}
        {formItemShow.task_no && (
          <FormItem label="极兔任务号" name="task_no">
            {getFieldDecorator('task_no', {
              rules: [
                {
                  pattern: /^[0-9a-zA-Z]+$/g,
                  message: '请输入正确的极兔任务号',
                },
              ],
            })(<Input maxLength={30} placeholder="请输入极兔任务号" />)}
          </FormItem>
        )}
        {formItemShow.TE_SHU_JIAN_JIAN_CHA && (
          <FormItem label="特殊件检查" name="TE_SHU_JIAN_JIAN_CHA">
            {getFieldDecorator('TE_SHU_JIAN_JIAN_CHA')(
              <SpecialCheck
                scanValue={scanValue}
                pageData={pageData}
                updatePageData={updatePageData}
                runSpecialCheckConfig={runSpecialCheckConfig}
              />,
            )}
          </FormItem>
        )}
        {formItemShow.need_dispatch && (
          <FormItem label="" name="need_dispatch">
            {getFieldDecorator('need_dispatch', {
              initialValue: true,
            })(
              <PatchPaijian cm_phone={courier_phone} signAutoDispatchData={signAutoDispatchData} />,
            )}
          </FormItem>
        )}
        {formItemShow.need_arrive && (
          <FormItem label="" name="need_arrive">
            {getFieldDecorator('need_arrive', {
              initialValue: true,
            })(
              <PatchDaojian
                cm_phone={courier_phone}
                dispatchAutoArriveData={dispatchAutoArriveData}
              />,
            )}
          </FormItem>
        )}
        {formItemShow.autoline_dispatch && (
          <FormItem label="" name="autoline_dispatch">
            {getFieldDecorator('autoline_dispatch', {
              initialValue: false,
            })(<AutoConfigPatch cm_phone={courier_phone} />)}
          </FormItem>
        )}
        {formItemShow.question && (
          <FormItem label="问题类型">
            {getFieldDecorator('question', {
              rules: [
                {
                  required: true,
                  message: '问题类型',
                },
              ],
            })(
              <PatchFormQuestion
                getFieldsValue={getFieldsValue}
                pageData={pageData}
                onHandleSelect={updatePageData}
              />,
            )}
          </FormItem>
        )}
        {formItemShow.lineInfo && (
          <FormItem label="线路">
            {getFieldDecorator('lineInfo', {
              rules: [
                {
                  required: true,
                  message: '请选择线路',
                },
              ],
            })(<PatchFormLine getFieldsValue={getFieldsValue} />)}
          </FormItem>
        )}
        {isGaipai && (
          <ul style={{ marginTop: '-10px' }}>
            <li>改派支持品牌:圆通</li>
            {redispatch_type == '1' ? (
              <li>改派给我:将上传运单改派给扫描员</li>
            ) : (
              <li>改派给他:将原派件员的派件包裹改派给新派件员</li>
            )}
            <li>可选派件员为配置圆通工号的业务员</li>
          </ul>
        )}
        <Row gutter={[12, 12]} className={styles.textArea}>
          <Col span={14}>
            <FormItem wrapperCol={24} noStyle>
              {getFieldDecorator('orders')(
                <TextArea
                  placeholder={`请输入运单号，并以“，”或者换行分割，最大限度${MAX_ORDER_LENGTH}个单号`}
                  style={{ height: 150 }}
                />,
              )}
            </FormItem>
          </Col>
          <Col span={10}>
            <PatchFormUpload
              getFieldsValue={getFieldsValue}
              validateFields={validateFields}
              onCheckOrderBrand={onCheckOrderBrand}
              pageData={pageData}
              allCourierNameMap={allCourierNameMap}
              redispatch_type={redispatch_type}
            />
          </Col>
        </Row>
        <Row gutter={[8, 8]} align="middle" justify="center">
          <Col span={14} style={{ display: 'flex', justifyContent: 'center' }}>
            <AuthorizedExtend auth="2" patchId>
              <Button type="primary" onClick={handleSubmit} loading={loading}>
                提交
              </Button>
            </AuthorizedExtend>
          </Col>
          <Col span={10}>
            <PatchFormTemplateModal pageData={pageData} />
          </Col>
        </Row>
      </Form>
    </Card>
  );
});

export default PatchForm;
