/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useMemo } from 'react';
import { ISearchForm } from '.';
import { Button } from 'antd';
import { isObject } from 'lodash';
import { SearchConfig } from '../types';
import ButtonLoading from '@/components/ButtonLoading';
import { KBProFormInstance } from '@/components/AdaptForm/form/types';
import React from 'react';
interface IProps extends ISearchForm {
  form?: KBProFormInstance;
}
export const useSubmitDom = (props: IProps) => {
  const { action, form, search } = props;
  const searchConfig = isObject(search) ? search as SearchConfig  : ({} as SearchConfig);
  const submit = async () => {
    await action?.reload(true);
  };
  const reset = async() => {
    const values = form.getFieldsValue()
    const arr = Object.keys(values).map(key=> values[key]).filter(Boolean)
    if(arr.length !== 0) {
      form?.resetFields();
     await action?.reload(true);
    }

  };
  const defaultSubmitDom = useMemo(
    () => {
      const list = [] as any

      list.push(
        <Button onClick={reset} key="reset">
          {searchConfig.resetText || '重置'}
        </Button>,
        <ButtonLoading key="submit" loading={action?.loading} type="primary" onFinish={submit}>
          {searchConfig.searchText || '查询'}
        </ButtonLoading>,
      );

      return list;
    },
    [form, searchConfig, action],
  );
  const _props = { ...searchConfig, form: { ...form, submit } };

  const submitDom = searchConfig.optionRender
    ? searchConfig.optionRender(_props, _props, defaultSubmitDom)
    : defaultSubmitDom;
  return {
    submitDom,
  };
};
