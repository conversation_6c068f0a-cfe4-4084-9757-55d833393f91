/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

@import '~antd/lib/style/themes/default.less';

.descriptionList {
  // offset the padding-bottom of last row
  :global {
    .ant-row {
      margin-bottom: -16px;
      overflow: hidden;
    }
  }

  .title {
    margin-bottom: 16px;
    color: @heading-color;
    font-weight: 500;
    font-size: 14px;
  }

  .term {
    display: table-cell;
    margin-right: 8px;
    padding-bottom: 16px;
    color: @heading-color;
    // Line-height is 22px IE dom height will calculate error
    line-height: 20px;
    white-space: nowrap;

    &::after {
      position: relative;
      top: -0.5px;
      margin: 0 8px 0 2px;
      content: ':';
    }
  }

  .detail {
    display: table-cell;
    width: 100%;
    padding-bottom: 16px;
    color: @text-color;
    line-height: 22px;
  }

  &.small {
    // offset the padding-bottom of last row
    :global {
      .ant-row {
        margin-bottom: -8px;
      }
    }
    .title {
      margin-bottom: 12px;
      color: @text-color;
    }
    .term,
    .detail {
      padding-bottom: 8px;
    }
  }

  &.large {
    .title {
      font-size: 16px;
    }
  }

  &.vertical {
    .term {
      display: block;
      padding-bottom: 8px;
    }

    .detail {
      display: block;
    }
  }
}
