/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import * as React from 'react';
import Description from './Description';

export interface IDescriptionListProps {
  layout?: 'horizontal' | 'vertical';
  col?: number;
  title: React.ReactNode;
  gutter?: number;
  size?: 'large' | 'small';
  style?: React.CSSProperties;
}

export default class DescriptionList extends React.Component<IDescriptionListProps, any> {
  public static Description: typeof Description;
}
