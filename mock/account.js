/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const account = (req, res) => {
  // 信息品牌列表
  const params = JSON.parse(req.body.data);
  const result = [];
  // const privilege = [
  //   'finance',
  //   'system',
  //   'business',
  //   'delivery',
  //   'shop',
  //   'post',
  //   'platform',
  //   'allocation',
  // ];
  // const range = Array.from({ length: 999 }).map((_, index) => `${index}-2`).join(',');
  switch (params.run) {
    case '/Platform/index':
      Array.from({
        length: 4,
      }).forEach((item, index) => {
        result.push({
          id: `${index}`,
          name: `张${index}`,
          phone: `**********${index}`,
          is_forbiden: Math.floor(Math.random() * 2),
          // privilege: '*',
          privilege:
            'business,delivery,allocation,orders,platform,post,system,finance,auto,downloadReport',
          sub_privilege_item: ['23-0', '24-1', '25-2'].join(','),
          blank_pwd: '123456aa',
          area_ids: index % 2 ? '*' : '1,3,5',
          area_name: index % 2 ? '全公司' : '嘉定片区、长宁片区',
        });
      });
      res.send({
        msg: '暂无结果',
        code: 0,
        data: {
          result,
          count: result.length,
        },
      });
      return;
    case '/Platform/addShopInfo':
      const { id } = req.body;
      res.send({
        msg: `已${id ? '修改' : '添加'}`,
        code: 0,
        data: {},
      });
      break;
    case '/Platform/delete':
      res.send({
        msg: '已删除',
        code: 0,
        data: {},
      });
      break;
    case '/Platform/privilege':
      res.send({
        msg: '已指定',
        code: 0,
        data: {},
      });
      break;
    case '/Platform/multiForbidden':
      const { is_forbiden } = req.body;
      const statuses = ['开启', '禁用'];
      res.send({
        msg: `已${statuses[is_forbiden]}`,
        code: 0,
        data: {},
      });
      break;
    case '/Api/Account/update':
      res.send({
        msg: '已更新',
        code: 0,
        data: {},
      });
      break;
    default:
      break;
  }
};

export { account };

export default {
  // 资金账户，订单明细列表
  'POST /v1/TotalDistribution/GpFinanceDetails/getFinanceDetailsList': (req, res) => {
    const list = [];
    Array.from({
      length: 10,
    }).map((_, index) => {
      list.push({
        date: `2021-8-1${index}`,
        count: '5',
        price: '0.100',
        brands: 'ht,yt',
        total_price: '0.5',
      });
    });
    res.send({
      msg: 'success',
      code: 0,
      data: list,
    });
  },
  // 资金账户，订单明细，导出
  'POST /v1/TotalDistribution/GpFinanceDetails/getFinanceDetailsByDate': (req, res) => {
    res.send({
      msg: 'success',
      code: 0,
      data: {},
    });
  },
  // 资金账户，账户余额，转入消费账户
  'POST /v1/TotalDistribution/GpFinanceDetails/transform': (req, res) => {
    res.send({
      msg: 'success',
      code: 0,
      data: {},
    });
  },
  // 资金账户，账户余额，修改余额预警
  'POST /Api/Company/editBalanceWarning': (req, res) => {
    res.send({
      msg: 'success',
      code: 0,
      data: {},
    });
  },
  // 资金账户，账户余额，获取余额预警
  'POST /Api/Company/shopBalanceWarningInfo': (req, res) => {
    res.send({
      code: 0,
      msg: '',
      data: {
        id: '5',
        shop_id: '8',
        kb_id: '7238',
        warning_balance: '301.00',
        warning_status: '1',
        create_time: '2021-12-15 09:38:29',
        update_time: '2021-12-15 09:38:29',
        phone: '15150731278,17766665555',
      },
    });
  },
  // 账号管理，给子账号分配片区
  'POST /Api/Site/divideAreaPower': (req, res) => {
    res.send({
      msg: 'success',
      code: 0,
      data: {},
    });
  },
};
