/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from 'mockjs';

const { mock } = mockjs;

export default {
  // 代入库，列表
  'POST /Api/ChinaPost/Dak/complate': (req, res) => {
    const { page = 1, pageSize = 20 } = req.body;
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          'list|10': [
            {
              cm_id: '@id',
              inn_name: '@cname',
              phone: '133****9999',
              'platform|1': ['申通', '圆通', '韵达', '圆通、中通'],
              update_time: '@date',
            },
          ],
          page,
          page_size: pageSize,
          total: 200,
        },
      }),
    );
  },
  'POST /Api/YZ/League/complate': (req, res) => {
    const { page = 1, pageSize = 20 } = req.body;
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          'list|10': [
            {
              cm_id: '@id',
              inn_name: '@cname',
              phone: '133****9999',
              'platform|1': ['申通', '圆通', '韵达', '圆通、中通'],
              update_time: '@date',
              'is_rules_new|1': [0, 1],
            },
          ],
          page,
          page_size: pageSize,
          total: 200,
        },
      }),
    );
  },
  // 代入库，详情
  'POST /Api/ChinaPost/Dak/complateDetail': (req, res) => {
    const { cm_id } = req.body;

    const info = cm_id
      ? [
          {
            platform: '圆通',
            username: '15519505110',
            password: '1233',
            status: '0',
            sms_code: 0,
            platform_brand: ['zt'],
          },
          {
            platform: '中通',
            username: '15885370856',
            password: '4321',
            status: '1',
            sms_code: 0,
            platform_brand: [],
          },
          {
            platform: '多多',
            username: '15822220856',
            password: '4321123',
            status: '1',
            sms_code: 1,
            platform_brand: ['jt', 'yd'],
          },
        ]
      : [
          {
            platform: '韵达',
            username: '',
            password: '',
            status: 0,
            sms_code: 0,
            platform_brand: [],
          },
          {
            platform: '中邮',
            username: '',
            password: '',
            status: 0,
            sms_code: 0,
            platform_brand: [],
          },
          {
            platform: '多多',
            username: '',
            password: '',
            status: 0,
            sms_code: 1,
            platform_brand: [],
          },
        ];

    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          cm_id: cm_id ? '4322090' : '',
          inn_name: cm_id ? '水城玉舍镇' : '',
          phone: cm_id ? '191****8540' : '',
          info,
          platform_brand: {
            sto: '申通',
            zt: '中通',
            yt: '圆通',
            yd: '韵达',
            jt: '极兔',
            jz: '邮政',
            ems: 'EMS',
          },
        },
      }),
    );
  },
  'POST /Api/YZ/League/complateDetail': (req, res) => {
    const { cm_id } = req.body;

    const info = cm_id
      ? [
          {
            platform: '圆通',
            username: '15519505110',
            password: '1233',
            status: '0',
            sms_code: 0,
            platform_brand: ['zt'],
          },
          {
            platform: '中通',
            username: '15885370856',
            password: '4321',
            status: '0',
            sms_code: 0,
            platform_brand: [],
          },
          {
            platform: '多多',
            username: '15822220856',
            password: '4321123',
            status: '1',
            sms_code: 1,
            platform_brand: ['jt', 'yd'],
          },
        ]
      : [
          {
            platform: '韵达',
            username: '',
            password: '',
            status: 0,
            sms_code: 0,
            platform_brand: [],
          },
          {
            platform: '中邮',
            username: '',
            password: '',
            status: 0,
            sms_code: 0,
            platform_brand: [],
          },
          {
            platform: '多多',
            username: '',
            password: '',
            status: 0,
            sms_code: 1,
            platform_brand: [],
          },
        ];

    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          cm_id: cm_id ? '4322090' : '',
          inn_name: cm_id ? '水城玉舍镇' : '',
          phone: cm_id ? '191****8540' : '',
          info: info.map(item => ({ ...item, ecommerce: 1 })),
          ecommerce: 1,
          platform_brand: {
            sto: '申通',
            zt: '中通',
            yt: '圆通',
            yd: '韵达',
            jt: '极兔',
            jz: '邮政',
            ems: 'EMS',
          },
        },
      }),
    );
  },
  // 代入库，新增修改
  'POST /Api/ChinaPost/Dak/complateAdd': (req, res) => {
    const { data } = req.body;
    const arr = JSON.parse(data);
    const index = arr.findIndex(v => v.platform == '多多');
    const { sms_code } = arr.find(v => v.platform == '多多');
    res.send(
      mock({
        code: sms_code ? 0 : index > 0 ? 19999 : 1000,
        msg: '成功ssssss',
        data:
          index > 0
            ? {
                sms_code: '多多',
              }
            : {},
      }),
    );
  },
  'POST /Api/YZ/League/complateAdd': (req, res) => {
    const { data } = req.body;
    const arr = JSON.parse(data);
    const index = arr.findIndex(v => v.platform == '多多');
    const { sms_code } = arr.find(v => v.platform == '多多');
    res.send(
      mock({
        code: sms_code ? 0 : index > 0 ? 19999 : 1000,
        msg: '成功ssssss',
        data:
          index > 0
            ? {
                sms_code: '多多',
              }
            : {},
      }),
    );
    // res.send(
    //   mock({
    //     code: 19991,
    //     msg: '',
    //     data: {},
    //   }),
    // );
  },
  // 代入库，导入
  'POST /Api/ChinaPost/Dak/complateImport': (req, res) => {
    res.send(
      mock({
        code: 1110,
        msg: '导入成功',
        data: {},
      }),
    );
  },
  'POST /Api/YZ/League/complateImport': (req, res) => {
    res.send(
      mock({
        code: 1110,
        msg: '导入成功',
        data: {},
      }),
    );
  },
  // 代入库，发送短信
  'POST /Api/ChinaPost/Dak/sendSmsCode': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '发送成功',
        data: {},
      }),
    );
  },
  'POST /Api/YZ/League/sendSmsCode': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '发送成功',
        data: {},
      }),
    );
  },
  // 场地扫描管理，快递员列表
  'POST /Api/YZ/League/sitescan': (req, res) => {
    const { page = 1, pageSize = 20 } = req.body;
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          'list|10': [
            {
              id: '@id',
              league_id: '@id',
              courier_id: '@id',
              create_at: '@datetime',
              update_at: '@datetime',
              'status|1': [0, 1],
              username: '15179185977',
              realname: '@cname',
            },
          ],
          page,
          page_size: pageSize,
          total: 200,
        },
      }),
    );
  },
  // 场地扫描管理，添加快递员
  'POST /Api/YZ/League/sitescanAdd': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: '',
      }),
    );
  },
  // 场地扫描管理，快递员启用/禁用
  'POST /Api/YZ/League/sitescanEdit': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: '',
      }),
    );
  },
  // 场地扫描管理，货架设置框数量详情
  'POST /Api/YZ/League/frameDetail': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: '@integer(1,1000)',
      }),
    );
  },
  // 场地扫描管理，货架框数量设置
  'POST /Api/YZ/League/frameEdit': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: '',
      }),
    );
  },
  'POST /Api/YZ/League/complateStatistics': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '',
        data: {
          'list|15': [
            {
              inn_name: '@cname',
              inn_phone: '@integer(13000000000, 18999999999)',
              // 'platform|1': ['cn', 'dd', 'kb'],
              total: '@integer(1, 100)',
              money: '@float(0, 100, 2, 2)',
              sto: '@integer(1, 100)',
              zt: '@integer(1, 100)',
              yt: '@integer(1, 100)',
              yd: '@integer(1, 100)',
              jt: '@integer(1, 100)',
              yz: '@integer(1, 100)',
              ems: '@integer(1, 100)',
              cm_id: '@id',
            },
          ],
          total: 60,
          size: 20,
          page: 1,
        },
      }),
    );
  },
  'POST /Api/YZ/League/complateBrandAll': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          list: {
            mmyz: '妈妈驿站',
            kdmarket: '快递超市',
            ydmarket: '韵达超市',
            zyet: '中邮E通',
            ddyz: '多多驿站',
            stmz: '申通喵站',
            cainiao: '菜鸟',
            linli: '邻里管家',
          },
        },
      }),
    );
  },
  'POST /Api/YZ/League/complateRulesDetail': (_, res) => {
    res.send({
      code: 0,
      data: {
        rules: [
          {
            brand: '极兔',
            brand_en: 'jt',
            items: [
              { type: 'all', items: [{ platform: 'cn', num: '10' }] },
              {
                type: 'pdd',
                items: [{ platform: 'cn', num: '10%' }, { platform: 'dd', num: '20%' }],
              },
            ],
          },
          {
            brand: '申通',
            brand_en: 'st',
            items: [{ type: 'cnys', items: [{ platform: 'cn', num: '10' }] }],
          },
        ],
        complates: ['yz', 'st', 'cn', 'dd'],
      },
    });
  },
  'POST /Api/YZ/League/complateRulesAdd': (_, res) => {
    res.send({
      code: 0,
      data: '',
      msg: '成功',
    });
  },
  'POST /Api/YZ/League/complateDel': (_, res) => {
    res.send({
      code: 0,
      data: '',
      msg: '成功',
    });
  },
  'POST /Api/YZ/League/getGraphicCode': (_, res) => {
    res.send({
      code: 0,
      data: {
        title: '安全验证',
        tip: '请依次点击：一 二 三',
        wordList: ['一', '二', '三'],
        captchaId: '79615ef9-b0d7-4f28-98ff-8405c129b932',
        img: 'https://cdn-img.kuaidihelp.com/qj/miniapp/activity/recruit/bg.png',
        width: '310',
        height: '155',
      },
    });
  },
};
