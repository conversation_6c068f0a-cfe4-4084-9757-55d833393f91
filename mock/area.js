/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export default {
  // 导入快递员
  'POST /Api/YZ/CourierStation/checkDak': (req, res) => {
    res.send({
      code: 0,
      msg: '审核成功',
      data: {},
    });
  },
  // 下属驿站充值列表
  'POST /Api/YZ/CourierStation/chargePosts': (req, res) => {
    const list = [];
    Array.from({
      length: 20,
    }).forEach((_, index) => {
      list.push({
        inn_name: `${index}_驿站名称`,
        cm_id: index,
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: list,
    });
  },
  // 下属驿站充值金额
  'POST /Api/YZ/League/rechargeInnByLeague': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 下属驿站充值短信、可消费余额
  'POST /Api/InnAccount/batchRecharge': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        order_number: Math.random(),
        total_money: 20,
        total_sms_count: '30',
        return_url: 'https://qr.alipay.com/bax00786paiuv4k4nk564018',
      },
    });
  },
  // 下属驿站充值，充值订单结果查询
  'POST /Api/InnAccount/queryRechargeResult': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        order_number: '*********',
        // pay_status: (Math.random() * 10) / 3 == 0 ? 0 : 1,
        money: '0.03',
        status: '2',
        dak_status: 0,
      },
    });
  },
  // 区域内驿站，驿站详情
  'POST /Api/ChinaPost/Dak/subDakInfo': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        // baseInfo: {
        //   cm_name: "驿站名称",
        //   registe_account: "***********",
        //   cm_code: "123456",
        //   contacts: "联系人",
        //   address: "驿站注册地址",
        //   registe_time: "注册时间",
        //   verify_time: "审核时间",
        // },
        // businessInfo: {
        //   branch: [
        //     {
        //       id: "0",
        //       pid: "-1",
        //       name: "中国邮政总公司",
        //       level: "0",
        //     },
        //     {
        //       id: "1",
        //       name: "浙江省",
        //       level: "1",
        //       pid: "0",
        //     },
        //     {
        //       id: "2",
        //       name: "杭州市",
        //       level: "2",
        //       pid: "1",
        //     },
        //     {
        //       id: "3",
        //       pid: "2",
        //       name: "西湖区",
        //       level: "3",
        //     },
        //     {
        //       id: "4",
        //       pid: "3",
        //       name: "桥西支局",
        //       level: "4",
        //     },
        //   ],
        //   relation: "邮政自营",
        //   source: "新建驿站",
        //   workplace_info: "自有局房",
        //   business_pattern: "邮乐购站点叠加",
        //   zone: "城区",
        //   workplace_type: "城区",
        //   organization_code: 123456,
        // },

        id: '48',
        cm_id: '2225699',
        branch_id: '9224219',
        status: '1',
        relation: '自主经营',
        source: '其他驿站转型',
        workplace_info: '经营者自有（租用）',
        business_pattern: '邮乐购站点叠加',
        zone: '城郊结合部',
        workplace_type: '写字楼',
        mechanism_no: '1231231',
        create_at: '2021-09-17 13:24:38',
        update_at: '2021-09-17 13:42:41',
        inn_name: '',
        concat_name: '',
        phone: '***********',
        concat_phone: '***********',
        province: '',
        city: '',
        district: '',
        town: '',
        concat_location: '',
        station_code: '0',
        join_at: '2021-09-17 13:24:38',
        area_type: '农村',
        province_code: '330000000000',
        branch: [
          {
            id: '0',
            pid: '-1',
            name: '中国邮政总公司',
            level: '0',
          },
          {
            id: '1',
            name: '浙江省',
            level: '1',
            pid: '0',
          },
          {
            id: '2',
            name: '杭州市',
            level: '2',
            pid: '1',
          },
          {
            id: '3',
            pid: '2',
            name: '西湖区',
            level: '3',
          },
          {
            id: '4',
            pid: '3',
            name: '桥西支局',
            level: '4',
          },
        ],
      },
    });
  },
  // 区域内驿站，修改驿站经营信息
  'POST /Api/YZ/CourierStation/modifyPostInfo': (req, res) => {
    res.send({
      code: 0,
      msg: '审核成功',
      data: {},
    });
  },
  // 中邮下属驿站账户信息列表
  'POST /Api/ChinaPost/Dak/getAccountList': (req, res) => {
    const list = Array.from({ length: 45 }).map((_, index) => ({
      id: index,
      cm_id: index,
      branch_id: '9223452',
      status: '1',
      relation: '',
      source: '',
      workplace_info: '',
      business_pattern: '',
      zone: '',
      workplace_type: '',
      mechanism_no: '',
      create_at: '2020-11-05 17:52:01',
      update_at: '2022-03-01 17:04:09',
      kb_id: index,
      inn_name: `${index}驿站名称`,
      phone: '***********',
      sms_balance: 4,
      avail_money: 90,
    }));

    res.send({
      code: 0,
      msg: 'success',
      data: {
        total: '45',
        page: 1,
        size: 20,
        list,
      },
    });
  },
  // 新零售，驿站审核通过或决绝
  '/Api/YZ/CourierStation/checkDak': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 片区列表
  'POST /Api/Site/getAreaSiteListByPhone': (req, res) => {
    const list = [];
    Array.from({
      length: 20,
    }).forEach((_, index) => {
      list.push({
        site_code: index,
        site_name: index == 0 ? '全公司_site' : `${index}_片区名称site`,
        site_id: index,
        site_charge: '3324',
        site_phone: '17621829941',
        is_inn_area: '2',
        is_gp_area: '2',
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: list,
    });
  },
  // 开启驿站片区管理或者共配片区管理的片区列表
  'POST /Api/Site/getAreaSiteList': (req, res) => {
    const list = [];
    Array.from({
      length: 20,
    }).forEach((_, index) => {
      list.push({
        site_code: index,
        site_name: `${index}_片区名称site`,
        site_id: `${index}`,
        site_charge: `${index}_片区负责人`,
        site_phone: '17621829941',
        is_inn_area: '2',
        is_gp_area: '2',
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: list,
    });
  },
  // 下属驿站片区分配
  'POST /Api/Site/divideInnAreaPower': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 片区列表
  'POST /Api/Site/getInnAreaSiteList': (req, res) => {
    const list = [];
    Array.from({
      length: 20,
    }).forEach((_, index) => {
      list.push({
        site_code: index,
        site_name: `${index}_片区名称site`,
        site_id: `${index}`,
        site_charge: '3324',
        site_phone: '17621829941',
        is_inn_area: '2',
        is_gp_area: '2',
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: list,
    });
  },
};
