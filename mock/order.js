/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable no-shadow */
import mockjs from 'mockjs';

const { mock } = mockjs;
// 获取订单
export function getOrderList(req, res) {
  const params = req.body;
  const orderList = [];
  for (let i = 0; i < 64; i += 1) {
    orderList.push({
      id: i + 1,
      brand: 'sto',
      courier_name: '老呵呵',
      customer_name: '30001',
      courier_no: '024588',
      order_id: '43414418',
      app_platform: 'COURIER_APP',
      shipper_type: '1',
      pay_type: '1',
      courier_phone: '***********',
      job_number: '000303001',
      node_no: '0003',
      site_no: '03',
      post_cost: '0.00',
      cod_cost: '0.00',
      insure_cost: '0.00',
      trade_name: '日用品',
      waybill_no: '66009009100801',
      waybill_type: '0',
      state: '1',
      is_insure: '1',
      is_cod: '1',
      business_id: '1f292b71efd12ad1352220f229d45d1280de553a',
      business_type: '9001',
      business_method: '',
      quantity: '1',
      weight: '10.1',
      volume: '12.1',
      remark: '小心轻放',
      commodities: '[]',
      sender_company: '',
      sender_post_code: '200083',
      sender_mobile: '***********',
      sender_name: '大飞哥',
      sender_tel: '021-55033123',
      sender_province: '上海',
      sender_city: '上海市',
      sender_district: '虹口区',
      sender_town: '',
      sender_address: '三门路1002弄29号301室',
      recipient_company: '',
      recipient_name: '大飞哥',
      recipient_mobile: '***********',
      recipient_tel: '021-65012345',
      recipient_post_code: '200090',
      recipient_province: '上海',
      recipient_city: '上海市',
      recipient_district: '杨浦区',
      recipient_town: '',
      recipient_address: '长阳路引翔港北街25号',
      updated_at: '2018-09-14 10:32:01',
      created_at: '2018-09-14 10:32:01',
      brand_id: '0',
      order_status: '已签收',
    });
  }

  let size = 10;
  if (params.size) {
    size = params.size * 1;
  }

  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      total: orderList.length,
      size,
      page: parseInt(params.currentPage, 10) || 1,
      result: orderList,
    },
  });
}
// 获取订单明细列表
export function getOrderDetailsList(req, res) {
  const params = req.body;
  const accountLength = 100;
  const orderDetailsList = new Array(accountLength);
  for (let i = 0; i < accountLength; i += 1) {
    orderDetailsList[i] = {
      brand: '申通',
      chann: '定制公众号-业务员',
      channel: 'minpost',
      charging_weight: '12',
      collect_courier_id: '237',
      collect_courier_mobile: '***********',
      collect_courier_name: '二黄测',
      complete_at: '2021-08-16 17:21:02',
      courierInfo: '姓名：二黄测<br>电话：***********',
      create_at: '2021-08-16 17:20:51',
      freight: null,
      getInfo:
        '姓名：收<br>电话：***********<br>地址：上海市上海市长宁区新泾镇麦当劳(建滔商业广场店)建滔商业广场',
      id: `***************${i}`,
      isMonthly: '0',
      is_print: '0',
      iscountAmount: {
        money: 1,
        type: '优惠券',
      },
      orderCategory: '定制公众号',
      package_info: '日用品',
      package_note: '',
      payInfo: {
        money: 20,
        status: '已支付',
        type: '下单现付',
        time: '2021-08-16 17:20:51',
      },
      pickup_code: '28601957',
      receipts_under_custody: '0.00',
      sendInfo:
        '姓名：测试忽略<br>电话：13488888888<br>地址：天津市天津市静海区新泾镇星巴克(建滔广场店)建滔商业广场',
      shipper_address: '新泾镇星巴克(建滔广场店)建滔商业广场',
      shipper_city: '天津市',
      shipper_district: '静海区',
      shipper_mobile: '13488888888',
      shipper_name: '测试忽略',
      shipper_province: '天津市',
      shipper_uid: '1950462',
      shipping_address: '新泾镇麦当劳(建滔商业广场店)建滔商业广场',
      shipping_area_id: '0',
      shipping_city: '上海市',
      shipping_district: '长宁区',
      shipping_mobile: '***********',
      shipping_name: '收',
      shipping_province: '上海市',
      source: 'special-public',
      status: '3',
      statusText: '已受理',
      update_at: '2021-08-16 17:23:29',
      waybillId: '776006507558221',
      waybill_no: i % 2 ? '776006507558221' : '',
      weight: '2',
    };
  }
  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      list: orderDetailsList,
      pageNum: orderDetailsList.length,
      page: parseInt(params.page, 10) || 1,
    },
  });
}
// 站点列表
export function getStationList(req, res) {
  const stationList = [];
  for (let i = 0; i < 10; i += 1) {
    stationList.push(
      mock({
        site_code: /\d{7}$/,
        site_id: /\d{7}$/,
        site_name: '@csentence(4)',
        site_charge: '@cname',
        site_phone: /1[1-9]\d{9}$/,
        is_gp_area: i % 3 ? 1 : 2,
        is_inn_area: i % 3 ? 2 : 1,
      }),
    );
  }
  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      result: [
        {
          site_code: '00',
          site_name: '主站123',
          site_id: '10',
          site_charge: '快宝急送123',
          site_phone: '13377778888',
          is_inn_area: '2',
          is_gp_area: '1',
        },
        {
          site_code: '01',
          site_name: '站点',
          site_id: '12',
          site_charge: '18721008363',
          site_phone: '18721008362',
          is_inn_area: '1',
          is_gp_area: '1',
        },
        {
          site_code: '02',
          site_name: '站点3',
          site_id: '13',
          site_charge: '站点3nameq',
          site_phone: '18721008000',
          is_inn_area: '1',
          is_gp_area: '2',
        },
        {
          site_code: '05',
          site_name: '费测站点（不要删）',
          site_id: '77',
          site_charge: '费测',
          site_phone: '13524849951',
          is_inn_area: '1',
          is_gp_area: '2',
        },
        {
          site_code: '08',
          site_name: 'ces',
          site_id: '80',
          site_charge: 'ces',
          site_phone: '13621828888',
          is_inn_area: '1',
          is_gp_area: '2',
        },
      ],
      company_info: {
        0: {
          brand_code: '0002',
          brand_name: '上海第一站',
          id: '13',
        },
      },
    },
  });
}

//  获取业务员列表
export function getOperatorList(req, res) {
  const params = req.body;
  const operatorList = [];
  const { keyWords } = params;
  for (let i = 0; i < 40; i += 1) {
    operatorList.push({
      id: i + 1,
      courier_no: `00001${i}`,
      courier_code: `001${i}`,
      courier_name: `李${i + 1}名`,
      courier_phone: '18211111111' + i,
      switch: i % 2 === 0 ? 1 : 0,
      barSwitch: i % 2 === 0 ? 1 : 0,
      num: {
        can_change_courier: '1',
      },
      brand: i % 2 === 0 ? '申通、中通、韵达' : '申通、中通',
      company_site_id: keyWords ? '13' : '0', // 站点id用于查询后选中站点
      // disabled: i % 2 === 0 ? 1 : 0,
      sf_id: `001${i}`,
      can_change_courier: i % 2 === 0 ? 1 : 0,
      is_disable: i % 2 === 0 ? 1 : 0,
      status: i % 2 === 0 ? '已确认' : '待确认',
    });
  }
  let size = 10;
  if (params.size) {
    size = params.size * 1;
  }
  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      total: operatorList.length,
      size,
      page: parseInt(params.currentPage || params.page, 10) || 1,
      result: keyWords ? [operatorList[0]] : operatorList,
    },
  });
}

//  下属驿站列表
export function subList(req, res) {
  const { page } = req.body;
  const operatorList = [];
  for (let i = (page - 1) * 15; i < page * 15; i += 1) {
    operatorList.push({
      phone: '13721213152',
      concat_phone: `1372121315${i}`,
      address: '',
      province: '安徽',
      city: '芜湖',
      district: '繁昌县',
      cm_id: `1579494${i}`,
      kb_id: `15_kb${i}`,
      concat_name: '大飞哥',
      create_at: '2018-10-16 17:44:41',
      area: '安徽 芜湖 繁昌县',
      inn_name: `驿站${i}`,
      company_name: `驿站${i}`,
      join_time: '2018-11-16 10:44:41',
      apply_status: '',
      storage_num: `15494${i}`,
      sms_balance: 10,
      avail_money: 20,
      yesterday_in_num: -1,
      in_growth_num: 0,
      qrcode:
        'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQFc8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyWlp0WDV0UFdlSGwxMDAwMHcwM20AAgRF_hFcAwQAAAAA',
      area_name: `片区———${i}`,
      site_id: i,
      id: `${i}_id`,
      open_video: i % 2 === 0 ? 1 : 0,
      is_ec: i % 2 === 0 ? 1 : 2,
      ec_forbid_use: i % 2 === 0 ? 1 : 2,
      forbid_deliver: i % 3 === 0 ? 1 : 2,
    });
  }
  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      list: page > 5 ? [] : operatorList,
      page: page > 5 ? 6 : page,
      total: 64,
      pageSize: 15,
    },
  });
}

// 获取驿站列表
export function getAreaList(req, res) {
  res.send({
    code: 0,
    msg: '获取成功',
    data: [
      {
        cm_id: '1167137',
        inn_name: '驿站',
        concat_phone: '驿站-17621321701',
        concat_location: '上海市 上海市 长宁区同协路269',
        todayStock_cnt: '5',
        detent_cnt: '5',
      },
      {
        cm_id: '1167164',
        inn_name: '拒绝者',
        concat_phone: 'zzz-18123456789',
        concat_location: '上海市 上海市 黄浦区777779999999',
        todayStock_cnt: '1780',
        detent_cnt: '1780',
      },
    ],
  });
}

// 获取品牌列表
export function getBrandsList(req, res) {
  res.send({
    code: 0,
    msg: '成功',
    data: [
      { brand: '申通', brand_en: 'sto', ali_brand: 'STO' },
      { brand: '中通', brand_en: 'zt', ali_brand: 'ZTO' },
      { brand: '韵达', brand_en: 'yd', ali_brand: 'YUNDA' },
      { brand: '百世', brand_en: 'ht', ali_brand: 'HTKY' },
      { brand: '天天', brand_en: 'tt', ali_brand: 'TTKDEX' },
      { brand: 'EMS', brand_en: 'ems', ali_brand: 'EYB' },
      { brand: '圆通', brand_en: 'yt', ali_brand: 'YTO' },
      { brand: '顺丰', brand_en: 'sf', ali_brand: 'SF' },
      { brand: '京东', brand_en: 'jd', ali_brand: 'JD' },
      { brand: '安能', brand_en: 'ane', ali_brand: 'ANE' },
      { brand: '德邦', brand_en: 'dp', ali_brand: 'DBKD' },
      { brand: '优速', brand_en: 'ys', ali_brand: 'UC' },
      { brand: '宅急送', brand_en: 'zjs', ali_brand: 'ZJS' },
      { brand: '汇文', brand_en: 'hw' },
      { brand: '邮政', brand_en: 'yz', ali_brand: 'EMS' },
      { brand: '唯品会', brand_en: 'wph' },
      { brand: '天猫超市', brand_en: 'tmcs' },
      { brand: '苏宁', brand_en: 'sn' },
      { brand: '中国联邦', brand_en: 'fedex' },
      { brand: '国通', brand_en: 'gt' },
      { brand: '极兔', brand_en: 'jt' },
      { brand: '快宝同城', brand_en: 'kbtc' },
      { brand: '丹鸟', brand_en: 'dn' },
      { brand: '众邮', brand_en: 'zykd' },
      { brand: '多多买菜', brand_en: 'ddmc' },
      { brand: '美团优选', brand_en: 'mtyx' },
      { brand: '橙心优选', brand_en: 'cxyx' },
      { brand: '兴盛优选', brand_en: 'xsyx' },
      { brand: '同程生活', brand_en: 'tcsh' },
      { brand: '十荟团', brand_en: 'sht' },
      { brand: '其他', brand_en: 'qita' },
      { brand: '丰网', brand_en: 'fw' },
    ],
  });
}

// 统计数据列表
export function getPostList(req, res) {
  const params = req.body;
  const orderList = [];
  for (let i = 0; i < 64; i += 1) {
    orderList.push({
      id: `6675${i}`,
      inn_id: `1167164${i}`,
      inn_name: '拒绝者',
      start: '2018-09-11',
      end: '2018-09-11',
      total: '0',
      success_num: '0',
      fail_num: '0',
      brand: '天猫超市',
      brand_cn: '天猫超市',
      storage_num: 0,
      out_num: 0,
      detent_num: 0,
    });
  }

  let page_num = 10;
  if (params.page_num) {
    page_num = params.page_num * 1;
  }

  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      count: orderList.length,
      page_num,
      page: parseInt(params.currentPage, 10) || 1,
      result: orderList,
      in_num_sum: 0,
      out_num_sum: 0,
      back_num_sum: 0,
    },
  });
}

// 统计折线
export function getLeague(req, res) {
  res.send({
    code: 0,
    msg: '获取成功',
    data: [
      {
        day: '',
        value: 1,
        London: 2,
      },
      {
        day: '2018-09-29',
        value: 2,
        London: 9,
      },
      {
        day: '2018-09-30',
        value: 3,
        London: 0,
      },
      {
        day: '2018-10-02',
        value: 4,
        London: 7,
      },
      {
        day: '2018-10-16',
        value: 0,
        London: 2,
      },
      {
        day: '2018-10-17',
        value: 0,
        London: 5,
      },
      {
        day: '2018-10-22',
        value: 0,
        London: 12,
      },
      {
        day: '2018-10-25',
        value: 10,
        London: 2,
      },
    ],
  });
}

// 饼图
export function getPies(req, res) {
  res.send({
    code: 0,
    msg: '获取成功',
    data: [
      {
        day: '中通',
        value: 1,
      },
      {
        day: '申通',
        value: 6,
      },
      {
        day: '圆通',
        value: 5,
      },
      {
        day: '韵达',
        value: 4,
      },
      {
        day: '安能',
        value: 1,
      },
      {
        day: '顺丰',
        value: 12,
      },
      {
        day: '京东',
        value: 8,
      },
      {
        day: '天天',
        value: 10,
      },
    ],
  });
}
// 柱状图
export function getHistogram(req, res) {
  const { type, code } = req.body;
  const length = code == 0 ? 5 : 3;

  const orderList = [];
  for (let i = 0; i < length; i += 1) {
    orderList.push({
      abbr_name: `${type}_区域${i + 1}___${code}`,
      num: type == 'out_rate' ? `1${i}` : `1${i}23`,
      city_id: type == 'out_rate' ? `1${i}` : `1${i}23`,
    });
  }
  res.send({
    code: 0,
    msg: '获取成功',
    data: orderList,
  });
}
// 出库折线
export function storageSummary(req, res) {
  res.send({
    code: 0,
    msg: '获取成功',
    data: [
      {
        date: '2018-09-29',
        in_num: 1500,
        out_num: 2,
        back_num: 0,
      },
      {
        date: '2018-10-09',
        in_num: 2651,
        out_num: 2,
        back_num: '0',
      },
      {
        date: '2018-10-10',
        in_num: 3245,
        out_num: 2,
        back_num: '0',
      },
      {
        date: '2018-10-11',
        in_num: 4231,
        out_num: 2,
        back_num: '0',
      },
      {
        date: '2018-10-13',
        in_num: 2311,
        out_num: 2,
        back_num: '0',
      },
      {
        date: '2018-10-14',
        in_num: 3184,
        out_num: 2,
        back_num: '0',
      },
      {
        date: '2018-10-15',
        in_num: 2015,
        out_num: 2,
        back_num: '0',
      },
      {
        date: '2018-10-16',
        in_num: 2641,
        out_num: 2,
        back_num: '0',
      },
    ],
  });
}

// 出库饼图
export function storagePic(req, res) {
  const { type } = req.body;

  switch (type) {
    case 'in':
      res.send({
        code: 0,
        msg: '成功',
        data: [
          { brand: 'EMS', num: 45877 },
          { brand: '中通', num: 28126 },
          { brand: '圆通', num: 21138 },
          { brand: '韵达', num: 20370 },
          { brand: '极兔', num: 13719 },
          { brand: '申通', num: 12777 },
          { brand: '百世', num: 9438 },
          { brand: '邮政', num: 6891 },
          { brand: '顺丰', num: 1711 },
          { brand: '其他', num: 1493 },
        ],
      });
      break;
    case 'out':
      res.send({
        code: 0,
        msg: '成功',
        data: [
          {
            brand: '安能out',
            num: 9,
          },
          {
            brand: '德邦',
            num: 8,
          },
          {
            brand: 'EMS',
            num: 7,
          },
          {
            brand: '国通',
            num: 6,
          },
          {
            brand: '百世',
            num: 5,
          },
          {
            brand: '汇文',
            num: 4,
          },
          {
            brand: '京东',
            num: 3,
          },
          {
            brand: '快捷',
            num: 2,
          },
          {
            brand: '顺丰',
            num: 1,
          },
        ],
      });
      break;
    case 'order':
      res.send({
        code: 0,
        msg: '成功',
        data: [
          {
            brand: '安能order',
            num: 9,
          },
          {
            brand: '德邦',
            num: 8,
          },
          {
            brand: 'EMS',
            num: 7,
          },
          {
            brand: '国通',
            num: 6,
          },
          {
            brand: '百世',
            num: 5,
          },
          {
            brand: '汇文',
            num: 4,
          },
          {
            brand: '京东',
            num: 3,
          },
          {
            brand: '快捷',
            num: 2,
          },
          {
            brand: '顺丰',
            num: 1,
          },
        ],
      });
      break;
    default:
      res.send({
        code: 0,
        msg: '成功',
        data: [
          {
            brand: '安能',
            in_num: 1,
            out_num: 2,
            order_num: 22,
          },
          {
            brand: '德邦',
            in_num: 1,
            out_num: 2,
            order_num: 34,
          },
          {
            brand: 'EMS',
            in_num: 1,
            out_num: 2,
            order_num: 25,
          },
          {
            brand: '国通',
            in_num: 1,
            out_num: 4,
            order_num: 28,
          },
          {
            brand: '百世',
            in_num: 2,
            out_num: 8,
            order_num: 23,
          },
          {
            brand: '汇文',
            in_num: 1,
            out_num: 2,
            order_num: 42,
          },
          {
            brand: '京东',
            in_num: 1,
            out_num: 2,
            order_num: 21,
          },
          {
            brand: '快捷',
            in_num: 1,
            out_num: 2,
            order_num: 9,
          },
          {
            brand: '顺丰',
            in_num: 6,
            out_num: 6,
            order_num: 7,
          },
          {
            brand: '苏宁',
            in_num: 1,
            out_num: 0,
            order_num: 3,
          },
          {
            brand: '申通',
            in_num: 3,
            out_num: 3,
            order_num: 2,
          },
          {
            brand: '其他',
            in_num: 2,
            out_num: 1,
            order_num: 2,
          },
        ],
      });
      break;
  }
}

// 短信折线
export function smsSummary(req, res) {
  res.send({
    code: 0,
    msg: '成功',
    data: [
      { date: '2018-12-07', sms_num: 0 },
      { date: '2018-12-08', sms_num: 2 },
      { date: '2018-12-09', sms_num: 3 },
      { date: '2018-12-10', sms_num: 0 },
      { date: '2018-12-11', sms_num: 1 },
      { date: '2018-12-12', sms_num: 0 },
      { date: '2018-12-13', sms_num: 0 },
    ],
  });
}

// 订单折线
export function orderSummary(req, res) {
  res.send({
    code: 0,
    msg: '获取成功',
    data: [
      {
        date: '',
        num: 1,
        real_num: 2,
      },
      {
        date: '2018-09-29',
        num: 2,
        real_num: 9,
      },
      {
        date: '2018-09-30',
        num: 3,
        real_num: 0,
      },
      {
        date: '2018-10-02',
        num: 4,
        real_num: 7,
      },
      {
        date: '2018-10-16',
        num: 0,
        real_num: 2,
      },
      {
        date: '2018-10-17',
        num: 0,
        real_num: 5,
      },
      {
        date: '2018-10-22',
        num: 0,
        real_num: 12,
      },
      {
        date: '2018-10-25',
        num: 10,
        real_num: 2,
      },
    ],
  });
}

// 订单饼图
export function orderPic(req, res) {
  res.send({
    code: 0,
    msg: '获取成功',
    data: [
      {
        brand: '中通',
        num: 22,
        real_num: 1,
      },
      {
        brand: '申通',
        num: 6,
        real_num: 1,
      },
      {
        brand: '圆通',
        num: 5,
        real_num: 1,
      },
      {
        brand: '韵达',
        num: 4,
        real_num: 1,
      },
      {
        brand: '安能',
        num: 1,
        real_num: 1,
      },
      {
        brand: '顺丰',
        num: 12,
        real_num: 1,
      },
      {
        brand: '京东',
        num: 8,
        real_num: 1,
      },
      {
        brand: '天天',
        num: 10,
        real_num: 1,
      },
    ],
  });
}

// 粉丝折线
export function fansSummary(req, res) {
  res.send({
    code: 0,
    msg: '获取成功',
    data: [
      {
        date: '',
        new_add: 1,
        total: 2,
      },
      {
        date: '2018-09-29',
        new_add: 2,
        total: 9,
      },
      {
        date: '2018-09-30',
        new_add: 3,
        total: 0,
      },
      {
        date: '2018-10-02',
        new_add: 4,
        total: 7,
      },
      {
        date: '2018-10-16',
        new_add: 0,
        total: 2,
      },
      {
        date: '2018-10-17',
        new_add: 0,
        total: 5,
      },
      {
        date: '2018-10-22',
        new_add: 0,
        total: 12,
      },
      {
        date: '2018-10-25',
        new_add: 10,
        total: 2,
      },
    ],
  });
}
// 30日入库率，饼图数据
export function inStorageRate(req, res) {
  res.send({
    code: 0,
    msg: '成功',
    data: [
      { date: '2020-12-31', rate: '15.76' },
      { date: '2021-01-01', rate: '16.30' },
      { date: '2021-01-02', rate: '16.76' },
      { date: '2021-01-03', rate: '17.07' },
      { date: '2021-01-04', rate: '17.50' },
      { date: '2021-01-05', rate: '17.75' },
      { date: '2021-01-06', rate: '17.88' },
    ],
  });
}
// 3日出库率，饼图数据
export function outStorageRate(req, res) {
  res.send({
    code: 0,
    msg: '成功',
    data: [
      { date: '2020-12-07', rate: 20 },
      { date: '2020-12-08', rate: 80 },
      { date: '2020-12-09', rate: 53 },
      { date: '2020-12-10', rate: 9 },
      { date: '2020-12-11', rate: 51 },
      { date: '2020-12-12', rate: 55 },
      { date: '2020-12-13', rate: 99 },
    ],
  });
}
// 出库率数据
export function outStorageRateList(req, res) {
  const list = [];
  Array.from({
    length: 60,
  }).forEach((val, index) => {
    list.push({
      id: `${index}`,
      start: '2021-3-1',
      end: '2021-3-8',
      area: `区域${index}`,
      brand: `快递品牌${index}`,
      reate: `9${index}.00%`,
    });
  });
  res.send({
    code: 0,
    msg: '成功',
    data: {
      list,
      page: 1,
      total: 60,
      pageSize: 10,
    },
  });
}
// 每周投诉率，折线图数据
export function eachWeekComplaintRate(req, res) {
  res.send({
    code: 0,
    msg: '成功',
    data: [
      { date: '2020-12-07', rate: 10 },
      { date: '2020-12-08', rate: 200 },
      { date: '2020-12-09', rate: 300 },
      { date: '2020-12-10', rate: 88 },
      { date: '2020-12-11', rate: 1000 },
      { date: '2020-12-12', rate: 780 },
      { date: '2020-12-13', rate: 400 },
    ],
  });
}
// 管辖区域
export function getbrandList(req, res) {
  const operatorList = [];
  for (let i = 0; i < 3; i += 1) {
    operatorList.push({
      company_name: '白坪新区图文广告',
      province: '河南',
      city: '郑州',
      district: '登封市',
      address: '白坪新区实验学校斜对面',
      agency_area: [
        {
          city: '上海',
          province: '上海',
          district: '黄浦区, 长宁区',
        },
        {
          city: '北京',
          district: '西城区',
          province: '北京',
        },
      ],
    });
  }
  res.send({
    code: 0,
    msg: '获取成功',
    data: {},
  });
}

// 商城状态
export function getStatus(req, res) {
  res.send({
    code: 0,
    msg: 'success',
    data: {
      status: '免费开通',
      url: 'https://wdadmin.kuaidihelp.com/wdreg/loginform',
      response: '审核通过',
    },
  });
}

// 申请开通
export function getOpen(req, res) {
  res.send({
    code: 1,
    data: {},
    msg: '申请成功',
  });
}

// 出库详情列表
export function storageList(req, res) {
  const params = req.body;
  const orderList = [];
  const { brand } = params;
  for (let i = 0; i < 64; i += 1) {
    orderList.push({
      id: `6675${i}`,
      inn_id: `1167164${i}`,
      master_id: '1',
      dak_id: '1167164',
      company_name: '222',
      brand: `${brand}`,
      brand_zh: `${brand == 'sto' ? '申通' : '韵达'}`,
      inn_type: '0',
      origin_created_time: '2018-11-19',
      unknow_num: '3',
      created_time: '2018-12-11 16:43:32',
      in_num: '0',
      out_num: '0',
      back_num: '0',
    });
  }

  let page_num = 10;
  if (params.page_num) {
    page_num = params.page_num * 1;
  }

  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      total: orderList.length,
      page_num,
      page: parseInt(params.currentPage, 10) || 1,
      data: orderList,
      in_num_sum: 31,
      out_num_sum: 1,
      back_num_sum: 5,
    },
  });
}

// 短信详情列表
export function smsList(req, res) {
  const params = req.body;
  const orderList = [];
  for (let i = 0; i < 64; i += 1) {
    orderList.push({
      id: `6675${i}`,
      inn_id: `1167164${i}`,
      inn_name: '拒绝者',
      total: '0',
      date: '2018-08-25',
    });
  }

  let page_num = 10;
  if (params.page_num) {
    page_num = params.page_num * 1;
  }

  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      total: orderList.length,
      page_num,
      page: parseInt(params.currentPage, 10) || 1,
      data: orderList,
      sum: 0,
    },
  });
}

// 短信汇总列表
export function smsSum(req, res) {
  const params = req.body;
  const orderList = [];
  for (let i = 0; i < 64; i += 1) {
    orderList.push({
      id: `6675${i}`,
      inn_id: `1167164${i}`,
      inn_name: '拒绝者',
      start: '2018-09-11',
      end: '2018-09-11',
      total: '0',
      wx_success_total: i,
      intelligent_notification_total: i,
    });
  }

  let page_num = 10;
  if (params.page_num) {
    page_num = params.page_num * 1;
  }

  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      count: orderList.length,
      page_num,
      page: parseInt(params.currentPage, 10) || 1,
      data: orderList,
      sum: 0,
      wx_sum: 123,
      intelligent_notification: 200,
    },
  });
}

// 出库汇总列表
export function storageSum(req, res) {
  const params = req.body;
  const orderList = [];
  for (let i = 0; i < 64; i += 1) {
    orderList.push({
      id: `6675${i}`,
      inn_id: `1167164${i}`,
      inn_name: '拒绝者',
      start: '2018-09-11',
      end: '2018-09-11',
      total: '0',
      success_num: '0',
      fail_num: '0',
      brand: 'all',
      brand_cn: '天猫超市',
      brand_zh: '全部',
      storage_num: 0,
      out_num: 0,
      detent_num: 0,
    });
  }

  let page_num = 10;
  if (params.page_num) {
    page_num = params.page_num * 1;
  }

  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      count: orderList.length,
      page_num,
      page: parseInt(params.currentPage, 10) || 1,
      data: orderList,
      in_num_sum: 20,
      out_num_sum: 30,
      back_num_sum: 5,
    },
  });
}

// 订单详情列表
export function orderList(req, res) {
  const params = req.body;
  const orderList = [];
  for (let i = 0; i < 64; i += 1) {
    orderList.push({
      id: `6675${i}`,
      inn_id: `1167164${i}`,
      master_id: '1',
      dak_id: '1167164',
      company_name: '222',
      brand: 'yt',
      brand_zh: '申通',
      inn_type: '0',
      origin_created_time: '2018-11-19',
      unknow_num: '3',
      created_time: '2018-12-11 16:43:32',
      in_num: '0',
      out_num: '0',
      back_num: '0',
    });
  }

  let page_num = 10;
  if (params.page_num) {
    page_num = params.page_num * 1;
  }

  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      total: orderList.length,
      page_num,
      page: parseInt(params.currentPage, 10) || 1,
      data: orderList,
      in_num_sum: 0,
      out_num_sum: 0,
      back_num_sum: 0,
    },
  });
}

// 订单汇总列表
export function orderSum(req, res) {
  const params = req.body;
  const orderList = [];
  for (let i = 0; i < 64; i += 1) {
    orderList.push({
      id: `6675${i}`,
      inn_id: `1167164${i}`,
      inn_name: '拒绝者',
      start: '2018-09-11',
      end: '2018-09-11',
      total: '0',
      success_num: '0',
      fail_num: '0',
      brand: 'all',
      brand_cn: '天猫超市',
      brand_zh: '全部',
      storage_num: 0,
      out_num: 0,
      detent_num: 0,
    });
  }

  let page_num = 10;
  if (params.page_num) {
    page_num = params.page_num * 1;
  }

  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      count: orderList.length,
      page_num,
      page: parseInt(params.currentPage, 10) || 1,
      data: orderList,
      num_sum: 0,
      realnum_sum: 0,
    },
  });
}

// 粉丝详情列表
export function dakFansEverydayStat(req, res) {
  const params = req.body;
  const orderList = [];
  for (let i = 0; i < 30; i += 1) {
    orderList.push({
      date: `2018-09-${i}`,
      dak_id: `1167202${i}`,
      dak_name: '测试测试',
      new_add: 2,
      total: 2,
    });
  }

  let page_num = 10;
  if (params.page_num) {
    page_num = params.page_num * 1;
  }

  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      total: orderList.length,
      page_num,
      page: parseInt(params.currentPage, 10) || 1,
      every: orderList,
      all_total: {
        new_add: 3,
        total: 3,
      },
    },
  });
}

// 粉丝汇总列表
export function dakFansSummaryStat(req, res) {
  const params = req.body;
  const orderList = [];
  for (let i = 0; i < 64; i += 1) {
    orderList.push({
      start: '2018-05-01',
      end: '2018-12-31',
      dak_id: '1167202',
      dak_name: '23',
      new_add: 3,
      total: 3,
    });
  }

  let page_num = 10;
  if (params.page_num) {
    page_num = params.page_num * 1;
  }

  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      count: orderList.length,
      page_num,
      page: parseInt(params.currentPage, 10) || 1,
      every: orderList,
      all_total: {
        new_add: 3,
        total: 3,
      },
    },
  });
}

// 申请加盟驿站列表
export function getApplyList(req, res) {
  res.send({
    code: 0,
    msg: '获取成功',
    data: {
      total: '29',
      list: [
        {
          id: '82',
          cm_id: '0',
          brand: '韵达',
          name: '新的测试',
          phone: '13661916491',
          delivery_num: '3000-5000票',
          picture_path: [
            'https://upload.kuaidihelp.com/inn/activity/2019_04/15/15cb44cb35a7ad272627990.jpg',
            'https://upload.kuaidihelp.com/inn/activity/2019_04/15/15cb44cbd21dd0514030636.png',
          ],
          location: '上海市 上海市 长宁区',
          province: '上海市',
          city: '上海市',
          district: '长宁区',
          create_time: '2020-04-30 15:55:06',
          wx_nick: '',
          openid: '',
          source: '0',
          league_id: '0',
          status: '0',
          agency_league: '加盟商名称张加盟',
        },
        {
          id: '77',
          cm_id: '0',
          brand: '百世  ',
          name: '测试',
          phone: '18217690146',
          delivery_num: '1000票以下',
          picture_path: '',
          location: '北京市 北京市 东城区',
          province: '北京市',
          city: '北京市',
          district: '东城区',
          create_time: '2020-03-10 17:18:52',
          wx_nick: '',
          openid: '',
          source: '3',
          league_id: '0',
          status: '0',
          agency_league: '',
        },
        {
          id: '74',
          cm_id: '0',
          brand: '中通  ',
          name: '华为浏览器',
          phone: '12385888896',
          delivery_num: '1000-3000票',
          picture_path: '',
          location: '安徽省 合肥市 庐江县',
          province: '安徽省',
          city: '合肥市',
          district: '庐江县',
          create_time: '2020-02-14 14:58:18',
          wx_nick: '',
          openid: '',
          source: '0',
          league_id: '0',
          status: '0',
          agency_league: '旭东集团',
        },
        {
          id: '73',
          cm_id: '0',
          brand: '中通 百世 其他  ',
          name: '略略略',
          phone: '15484848151',
          delivery_num: '5000票以上',
          picture_path: '',
          location: '北京市 北京市 东城区',
          province: '北京市',
          city: '北京市',
          district: '东城区',
          create_time: '2020-02-14 11:48:48',
          wx_nick: '',
          openid: '',
          source: '0',
          league_id: '0',
          status: '0',
          agency_league: '',
        },
        {
          id: '69',
          cm_id: '0',
          brand: '中通 韵达  ',
          name: '啦啦',
          phone: '15555555555',
          delivery_num: '3000-5000票',
          picture_path: '',
          location: '北京市 北京市 东城区',
          province: '北京市',
          city: '北京市',
          district: '东城区',
          create_time: '2020-02-14 10:36:49',
          wx_nick: '',
          openid: '',
          source: '0',
          league_id: '0',
          status: '0',
          agency_league: '',
        },
      ],
      page: '1',
    },
  });
}

export function getOrderWorkTopInfo(req, res) {
  res.send({
    code: 0,
    msg: '成功',
    data: {
      todayOrderNum: '0',
      todayCloseOrderNum: '0',
      todayOverNum: '0',
      freight: 0,
      payNum: '11',
      distributedNum: '22',
      codNum: '333',
    },
  });
}
// 获取导出任务列表
export function getOrderExportList(req, res) {
  const { page, pageSize } = req.body;
  res.send(
    mock({
      msg: '成功',
      code: 0,
      data: {
        [`list|${pageSize}`]: [
          {
            id: '@id()',
            'is_finish|1': [0, 1],
            file_path: '@cname()',
            add_time: '@date("yyyy-MM-dd")',
            file_name: '@cname()',
          },
        ],
        page,
        pageNum: pageSize,
      },
    }),
  );
}
// 订单管理工作台echart数据
export function getWorkEchartsData(req, res) {
  res.send({
    code: 0,
    msg: '成功',
    data: {
      month: {
        monthNum: '183',
        monthOverNum: '112',
        recentlyList: {
          '08-01': '0',
          '08-02': '22',
          '08-03': '110',
          '08-04': '49',
          '08-05': '2',
        },
        recentlyLists: {
          '08-01': '0',
          '08-02': '8',
          '08-03': '77',
          '08-04': '26',
          '08-05': '1',
        },
      },
      week: {
        weekNum: '183',
        weekOverNum: '112',
        weekrecentlyList: {
          '08-02': '22',
          '08-03': '110',
          '08-04': '49',
          '08-05': '2',
        },
        weekrecentlyLists: {
          '08-02': '8',
          '08-03': '77',
          '08-04': '26',
          '08-05': '1',
        },
      },
    },
  });
}
export function dataOverview(req, res) {
  res.send({
    msg: 'success',
    code: 0,
    data: {
      inNum: 300,
      outNum: 200,
      backNum: 200,
      smsNum: 200,
      sendNum: 200,
      postFee: 2000,
      outRate: 98.99,
      complainRate: 1.23,
      newAddStations: 700,
      totalStations: 19999,
      newAddFans: 349,
      totalFans: 88888,
      inRanking: [
        {
          name: '驿站1',
          num: 25,
        },
        {
          name: '驿站2',
          num: 24,
        },
        {
          name: '驿站3',
          num: 23,
        },
      ],
      outRnaking: [
        {
          name: '驿站11',
          rate: 99,
        },
        {
          name: '驿站22',
          num: 98,
        },
        {
          name: '驿站33',
          num: 97,
        },
      ],
    },
  });
}
// 中邮，区域排名，列表详情
export function rankDetail(req, res) {
  const { type } = req.body;
  let data = {};
  switch (type) {
    case 'in':
      data = {
        page: 1,
        total: 20,
        storage_total: '1000',
        out_total: '2000',
        back_total: '3000',
        list: [
          {
            abbr_name: '浙江省',
            back_num: '0',
            brand: '全部',
            city_id: '334',
            date: '2021-04-19-2021-04-19',
            out_num: '0',
            storage_num: '0',
          },
          {
            date: '2021-01-01-2021-01-02',
            abbr_name: '区域1',
            brand: '全部',
            city_id: '2',
            storage_num: '入库数22333',
            out_num: '33',
            back_num: '44',
          },
        ],
      };
      break;
    case 'out_rate':
      data = {
        page: 1,
        total: 20,
        list: [
          {
            date: '2021-01-01-2021-01-02',
            abbr_name: '区域',
            brand: '全部',
            city_id: '1',
            out_rate: '出库率22',
          },
          {
            date: '2021-01-01-2021-01-02',
            abbr_name: '区域1',
            brand: '全部',
            city_id: '2',
            out_rate: '出库率22555',
          },
        ],
      };
      break;
    case 'order':
      data = {
        page: 1,
        total: 20,
        total_order_num: '1000',
        total_real_order_num: '1000',
        total_fee_num: '1000',
        list: [
          {
            date: '2021-01-01-2021-01-02',
            abbr_name: '区域',
            brand: '全部',
            city_id: '1',
            order_num: '订单数11',
            real_order_num: '22',
            fee_num: '55',
          },
          {
            date: '2021-01-01-2021-01-02',
            abbr_name: '区域3',
            brand: '全部',
            city_id: '2',
            order_num: '订单数22',
            real_order_num: '22',
            fee_num: '55',
          },
        ],
      };
      break;
    case 'fans':
      data = {
        page: 1,
        total: 20,
        total_new_fans: '1000',
        total_fans: '1000',
        list: [
          {
            date: '2021-01-01-2021-01-02',
            abbr_name: '区域',
            city_id: '1',
            new_fans: '100',
            grand_fans: '111',
          },
        ],
      };
      break;
    default:
      data = {
        page: 1,
        total: 0,
        list: [],
      };
      break;
  }

  res.send({
    code: 0,
    msg: '成功',
    data,
  });
}

/**
 * 获取对账单导出界面信息
 *  */
export function getExportInfo(req, res) {
  res.send(
    mock({
      msg: '成功',
      code: 0,
      data: {
        innList: [
          {
            inn_id: '2225683',
            inn_name: '工友超市',
          },
          {
            inn_id: '2775173',
            inn_name: '测试环境徐驿站',
          },
          {
            inn_id: '1614783',
            inn_name: '测试',
          },
        ],
        courierList: [
          {
            kdy_id: '2225674',
            courier_name: '徐肖磊',
          },
          {
            kdy_id: '2225908',
            courier_name: '肖磊333',
          },
          {
            kdy_id: '621441',
            courier_name: '管理员',
          },
        ],
        sendreceiveInfo: {
          sendInfo: '发件人（姓名丶电话丶省市区）',
          senderAddress: '发件人详细地址',
          receiveInfo: '收件人（姓名丶电话丶省市区）',
          receiverAddress: '收件人详细地址',
          customerName: '客户名称',
        },
        orderInfo: {
          orderNumber: '订单号',
          packageInfo: '商品类型',
          addService: '增值服务',
          packageNote: '备注',
          channel: '下单渠道',
          realName: '实名状态',
          packageStatus: '物流状态',
          paytypes: '收款类型',
        },
        lanjianInfo: {
          pickerName: '揽件员',
          pickerMobile: '揽件员电话',
          pickupCode: '揽件码',
        },
        weightPriceInfo: {
          paidFreight: '已收运费',
          discountFreight: '抵扣运费',
          actualFreight: '实重运费',
          quotationCharge: '报价单运费',
          chargingWeight: '实际重量',
          factWeight: '快递公司重量',
          packageWeight: '自填重量',
        },
        timeInfo: {
          createdTime: '订单创建时间',
          completedTime: '运单申请时间',
        },
        content: [
          'customerName',
          'packageInfo',
          'addService',
          'packageNote',
          'packageStatus',
          'packageWeight',
          'createdTime',
          'paidFreight',
          'sendInfo',
          'senderAddress',
          'receiveInfo',
          'receiverAddress',
          'orderNumber',
        ],
      },
    }),
  );
}
