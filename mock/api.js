/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from 'mockjs';
import {
  getOrderList,
  getOrderDetailsList,
  getStationList,
  getOperatorList,
  getAreaList,
  getBrandsList,
  getPostList,
  subList,
  getLeague,
  getbrandList,
  getStatus,
  getOpen,
  getPies,
  storageSummary,
  storagePic,
  smsSummary,
  orderSummary,
  orderPic,
  storageList,
  storageSum,
  smsSum,
  smsList,
  orderSum,
  orderList,
  dakFansEverydayStat,
  dakFansSummaryStat,
  fansSummary,
  getApplyList,
  getOrderWorkTopInfo,
  getWorkEchartsData,
  eachWeekComplaintRate,
  inStorageRate,
  outStorageRate,
  dataOverview,
  getHistogram,
  outStorageRateList,
  rankDetail,
  getOrderExportList,
  getExportInfo,
} from './order';

const { mock } = mockjs;

const delay = (then, time) => {
  const times = [0, 100, 1000];
  setTimeout(then, time >= 0 ? time : times[Math.floor(Math.random() * times.length)]);
};

// 代码中会兼容本地 service mock 以及部署站点的静态数据
const proxy = {
  // 获取当前用户信息
  'GET /Api/CurrentUser': {
    $body: {
      code: 0,
      msg: '',
      data: {
        name: '快宝1号店',
        userid: '00000001',
        mobile: '15155414101',
      },
    },
  },
  // 登出
  'POST /Api/Auth/logout': (req, res) => {
    res.send({
      code: 0,
      msg: '退出登录',
      data: {},
    });
  },
  // 登录
  'POST /Api/Auth/login': (req, res) => {
    const { type, phone } = req.body;
    if (type === 'mobile' && phone === '18311111111') {
      res.send({
        code: 0,
        msg: '登录成功',
        data: {
          is_active: '0',
          phone: '18311111111',
          shop_id: '1',
          name: '上海天字1号login',
        },
      });
    } else {
      res.send({
        code: 1,
        msg: '请使用18311111111登录',
        data: {},
      });
    }
  },
  // 注册
  'POST /Api/Auth/register': (req, res) => {
    res.send({
      code: 0,
      msg: '注册成功',
      data: {},
    });
  },
  // 修改
  'POST /Api/Auth/resetPwd': (req, res) => {
    res.send({
      code: 0,
      msg: '修改成功',
      data: {},
    });
  },

  // 短信验证码
  'POST /Api/Verify/sendSms': (req, res) => {
    res.send({
      code: 0,
      msg: '验证码已发送',
      data: {
        ...req.body,
      },
    });
  },
  // 更新公司信息
  'POST /Api/Company/companyRegister': (req, res) => {
    const { description } = req.body;
    const len = description.length;
    res.send({
      code: len < 10 ? 1 : 0,
      msg: len < 10 ? '描述信息过短哦' : '公司信息已更新',
      data: {},
    });
  },
  // 获取更新信息
  'POST /Api/Company/info': (req, res) => {
    const { phone = '***********' } = req.body;
    delay(() => {
      res.send({
        code: 0,
        msg: '请完善公司信息~',
        data: {
          area_ids: '*',
          biz_modules: ['1', '2', '3', '4'],
          biz_menus: [
            {
              label: '驿站',
              value: 1,
              routes: ['post', 'downloadReport', 'finance/account', 'system', 'set', 'workOrder'],
            },
            {
              label: '共配',
              value: 2,
              routes: [
                'Allocation',
                'downloadReport',
                'automatic',
                'finance/account',
                'system',
                'set',
              ],
            },
            {
              label: '寄件',
              value: 3,
              routes: ['order', 'system', 'set', 'post/area', 'downloadReport'],
            },
            {
              label: '其他',
              value: 4,
              routes: [
                'business',
                'delivery',
                'platform',
                'downloadReport',
                'workOrder',
                'finance/account',
                'https://cityfinance.kuaidihelp.com',
                'system',
                'set',
              ],
            },
          ],
          gp_area_ids: ['1', '2', '3'],
          inn_area_ids: ['4', '5', '6'],
          pid: 0,
          roles: [
            'business',
            'delivery',
            'allocation',
            'orders',
            'platform',
            'post',
            'system',
            'finance',
            'fund',
            'account',
            'super',
            'automatic',
            'auto',
            'set/safe',
            'allocation/authSet',
            'downloadReport',
            'allocation/postOption',
            'allocation/fastMsg',
            'allocation/patch',
            'allocation/customScan',
            'allocation/statistics',
            'allocation/dispat',
            'allocation/printKey',
            'allocation/cloud_storage',
          ],
          sub_privilege_item: '',
          sub_privilege: [],
          brand: 'sf',
          brand_code: '0011',
          brand_name: '快宝驿站1',
          city: '上海市',
          description: '',
          detail: '通协路269号6号楼6楼A-B单元',
          district: '长宁区',
          id: '27',
          phone: '***********',
          parent_phone: '***********',
          province: '上海市',
          shop_id: '37',
          is_special: 0,
          is_sitescan: 1,
          disclaimer_status: 1, // 同意使用共配，1： 是， 0：否
          is_trace_code: 1,
          show_info: 0,
          charge_notice: false,
          user_info: {
            branchCode: '0',
            shortName: 'shortName中邮驿站',
            branchId: '0',
            branchName: '总公司',
            branchLevel: '0', // 账号层级，0：总公司、1：省、2：市、3：区县、4：支局
            user_type: '1', // 账号类型，1：新零售账号，2：中邮账号，3：驿加易
            branch: [
              {
                id: '-2',
                name: '全部地区',
                code: 'c0',
                level: '0',
                pid: '-1',
              },
              // {
              //   id: '1',
              //   name: '浙江省',
              //   level: '1',
              //   code: 'c1',
              //   pid: '0',
              // },
              // {
              //   "id": "2",
              //   "name": "杭州市",
              //   "level": "2",
              //   "code": 'c2',
              //   "pid": "1",
              // },
              // {
              //   'id': '3',
              //   'pid': '2',
              //   'name': "西湖区",
              //   "code": 'c3',
              //   'level': '3',
              // },
              // {
              //   'id': '4',
              //   'pid': '3',
              //   'name': "桥西支局",
              //   'level': '4',
              // },
            ],
            is_active: '1',
            kb_id: '5606',
            name: '快宝驿站111',
            phone,
            shop_id: '37',
            accessAuth: 1, // 账号管理的操作权限，0：可操作、1：不可操作
            realAccount: 1, // 是否是真账号，1：真，2：假
            show_individual: '1',
          },
        },
      });
    });
  },
  'POST /Api/OrderData/getOrderList': getOrderDetailsList,
  'POST /Api/Order/index': getOrderList,
  // 驿站，获取业务员站点列表
  'POST /Api/Site/list': getStationList,
  // 中邮，获取业务员站点列表
  'POST /Api/Site/listForChinaPost': getStationList,
  // 获取第三方业务员站点列表
  'POST /Api/Site/ListForPartner': getStationList,
  'POST /Api/Courier/list': getOperatorList,
  // 获取驿站列表
  'POST /Api/YZ/CourierStation/getInnsList': getAreaList,
  // 申请加盟驿站列表
  'POST /Api/YZ/CourierStation/applyList': getApplyList,
  // 获取品牌列表
  'POST /Api/YZ/CourierStation/getBrandsList': getBrandsList,
  // 数据统计
  'POST /Api/YZ/CourierStation/webCount': getPostList,
  // 下属驿站
  'POST /Api/YZ/CourierStation/subList': subList,
  // H5数据统计，总览数据
  'POST /api/dataOverview': dataOverview,
  // 统计折线
  'POST /Api/YZ/CourierStation/league': getLeague,
  // 饼图
  'POST /Api/YZ/CourierStation/pies': getPies,
  // 柱状图
  'POST /Api/InnStatictis/rank': getHistogram,
  // 出库折线
  'POST /Api/InnStatictis/storageSummary': storageSummary,
  // 出库饼图
  'POST /Api/InnStatictis/storagePic': storagePic,
  // 短信折线
  'POST /Api/InnStatictis/smsSummary': smsSummary,
  // 订单折线
  'POST /Api/InnStatictis/orderSummary': orderSummary,
  // 订单饼图
  'POST /Api/InnStatictis/orderPic': orderPic,
  // 微信折线
  'POST /Api/YZ/WxFansStat/dakFansStatByDay': fansSummary,
  // 出库汇总列表
  'POST /Api/InnStatictis/storageSum': storageSum,
  // 出库列表
  'POST /Api/InnStatictis/storageList': storageList,
  // 短信汇总列表
  'POST /Api/InnStatictis/smsSum': smsSum,
  // 短信列表
  'POST /Api/InnStatictis/smsList': smsList,
  // 订单汇总列表
  'POST /Api/InnStatictis/orderSum': orderSum,
  // 订单列表
  'POST /Api/InnStatictis/orderList': orderList,
  // 粉丝汇总列表
  'POST /Api/YZ/WxFansStat/dakFansSummaryStat': dakFansSummaryStat,
  // 粉丝列表
  'POST /Api/YZ/WxFansStat/dakFansEverydayStat': dakFansEverydayStat,
  // 30日入库率
  'POST /Api/InnStatictis/inStorageRate': inStorageRate,
  // 3日出库率
  'POST /Api/InnStatictis/outStorageRate': outStorageRate,
  // 3日出库率列表
  'POST /Api/InnStatictis/outStorageRateList': outStorageRateList,
  // 每周投诉率
  'POST /Api/InnStatictis/complainRate': eachWeekComplaintRate,
  // 每周投诉率
  'POST /Api/InnStatictis/rankDetail': rankDetail,
  // 管辖区域
  'POST /Api/BusinessCooperation/brandList': getbrandList,
  // 商城状态审核
  'POST /Api/BusinessCooperation/getStatus': getStatus,
  // 申请开通
  'POST /Api/BusinessCooperation/save': getOpen,
  'POST /Api/OrderData/getInfo': getOrderWorkTopInfo,
  'POST /Api/OrderData/getOrderInfo': getWorkEchartsData,
  'POST /Api/OrderData/getExportList': getOrderExportList,
  // 添加驿站
  'POST /Api/YZ/CourierStation/addStation': (req, res) => {
    res.send({
      code: 0,
      msg: '添加成功！',
      data: {},
    });
  },
  // 关闭驿站
  'POST /Api/YZ/CourierStation/closeStation': (req, res) => {
    res.send({
      code: 0,
      msg: '关闭成功！',
      data: {},
    });
  },
  'POST /Api/Site/addSite': (req, res) => {
    res.send({
      code: 0,
      msg: '已添加',
      data: {
        site_name: '12345678aA',
        site_charge: '***********',
        site_phone: '***********',
        shop_id: '926063',
        company_id: '24',
        site_code: '00',
        site_id: '45',
        insert_id: 11,
      },
    });
  },
  // 第三方，添加站点
  'POST /Api/Site/editSiteForPartner': (req, res) => {
    res.send({
      code: 0,
      msg: '已添加',
      data: {
        site_name: '12345678aA',
        site_charge: '***********',
        site_phone: '***********',
        shop_id: '926063',
        company_id: '24',
        site_code: '00',
        site_id: '45',
        insert_id: 11,
      },
    });
  },
  'POST /Api/Site/delSite': (req, res) => {
    res.send({
      code: 0,
      msg: '已删除',
      data: {},
    });
  },
  // 第三方站点删除
  'POST /Api/Site/delSiteForPartner': (req, res) => {
    res.send({
      code: 0,
      msg: '已删除',
      data: {},
    });
  },
  'POST /Api/Courier/editCourier': (req, res) => {
    res.send({
      code: 0,
      msg: '已保存',
      data: {
        code: 1002312,
      },
    });
  },
  // 添加业务员
  'POST /Api/Courier/addCourier': (req, res) => {
    res.send({
      code: 0,
      msg: '已保存',
      data: {
        code: 1002312,
      },
    });
  },
  // 中邮，添加业务员
  'POST /Api/Courier/addCourierForChinaPost': (req, res) => {
    res.send({
      code: 0,
      msg: '已保存',
      data: {
        code: 1002312,
      },
    });
  },
  // 删除业务员
  'POST /Api/deleteOperator': (req, res) => {
    res.send({
      code: 0,
      msg: '已删除',
      data: {},
    });
  },
  // 站点业务员列表
  'POST /Api/Courier/siteList': (req, res) => {
    res.send(
      mock({
        code: '0',
        msg: '',
        'data|5-10': [
          {
            'id|+1': 1,
            courier_no: '@id',
            courier_code: '@id',
            courier_name: '@cname',
            courier_phone: /\d{11}/,
          },
        ],
      }),
    );
  },
  // 从下属驿站中删除
  'POST /Api/YZ/CourierStation/delSubDak': (req, res) => {
    res.send({
      code: 0,
      msg: '删除成功',
      data: {},
    });
  },
  // 获取验证码
  'POST /Api/Verify/sendCode': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  // 申请关闭驿站
  'POST /Api/YZ/CourierStation/applyCloseDak': (req, res) => {
    res.send({
      code: 0,
      msg: '申请成功',
      data: 8,
    });
  },
  // 申请关闭驿站
  'POST /Api/YZ/CourierStation/noPwdLogin': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  // 获取快宝驿站所属区域
  'POST /Api/YZ/League/stepGetArea': (req, res) => {
    let list = [];
    const code = req.body.code || 'c0';

    switch (code) {
      case 'c0':
        list = [
          {
            id: '1',
            code: 'c1',
            pid: '0',
            name: '浙江省',
            level: '1',
          },
          {
            id: '10',
            code: 'c10',
            pid: '0',
            name: '江苏省',
            level: '1',
          },
        ];
        break;
      case 'c1':
        list = [
          {
            id: '2',
            code: 'c2',
            pid: '1',
            name: '杭州市',
            level: '2',
          },
          {
            id: '100',
            code: 'c100',
            pid: '1',
            name: '宁波市',
            level: '2',
          },
        ];
        break;
      case 'c2':
        list = [
          {
            id: '3',
            code: 'c3',
            pid: '2',
            name: '西湖区',
            level: '3',
          },
          {
            id: '5',
            code: 'c5',
            pid: '2',
            name: '拱墅区',
            level: '3',
          },
        ];
        break;
      case 'c3':
        list = [
          {
            id: '4',
            code: 'c4',
            pid: '3',
            name: '桥西支局',
            level: '4',
          },
        ];
        break;
      case 'c4':
        list = [
          {
            id: '5',
            code: 'c5',
            pid: '6',
            name: '乡',
            level: '5',
          },
        ];
        break;
      default:
        list = [];
    }
    res.send({
      msg: '获取成功',
      code: 0,
      data: list,
    });
  },
  // 数据统计，获取昨日总览数据
  'POST /Api/InnStatictis/stat': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        in_num: 10,
        out_num: 20,
        back_num: 12,
        sms_num: 38,
        order_num: 52,
        fee_num: 21,
        station_num: 13,
        fans_num: 13,
        out_rate: 98.2,
        complaint_rate: 33,
        yz_in_num: 3,
        yz_out_num: 3,
        yz_out_rate: 98,
        total_station_num: 34,
        total_fans_num: 88,
        in_growth_num: 20,
        out_growth_num: -20,
      },
    });
  },
  // 设置管理，报表数据推送设置，获取列表
  'POST /Api/YZ/League/listUsers': (req, res) => {
    const stationList = [];
    for (let i = 0; i < 10; i += 1) {
      stationList.push({
        id: `${i}`,
        name: `赵${i}`,
        phone: `18311111111${i}`,
      });
    }
    res.send({
      code: 0,
      msg: 'success',
      data: stationList,
    });
  },
  // 设置管理，报表数据推送设置，添加
  'POST /Api/YZ/League/addNoticeUser': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  // 设置管理，报表数据推送设置，删除
  'POST /Api/YZ/League/deleteUser': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  // 设置管理，通知管理，获取列表
  'POST /Api/YZ/League/notice': (req, res) => {
    const list = [];
    for (let i = 0; i < 45; i += 1) {
      list.push({
        id: `${i}`,
        title: `关于XXXX${i}通知`,
        content: `通知内容通知内容通知内容通知内容通知内容${i}`,
        status: `${i % 3 ? (i % 2 ? '1' : '3') : i % 5 ? '2' : '4'}`,
        created_time: '2021-02-08 17:21:23',
      });
    }
    res.send({
      code: 0,
      msg: 'success',
      data: {
        list,
        total: '45',
        page_size: '30',
        page: '1',
      },
    });
  },
  // 设置管理，通知管理，删除
  'POST /Api/YZ/League/deleteNotice': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  // 设置管理，通知管理，保存通知（提交审核）
  'POST /Api/YZ/League/addNotice': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  // 设置管理，通知管理，保存通知（提交审核）
  'POST /Api/YZ/League/updateNotice': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  // 设置管理，通知管理，获取上传图片URL
  'POST /Api/Yz/League/uploadImage': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: 'http://wx2.sinaimg.cn/large/705793b8gy1g503pv9yazj20jy0imgmf.jpg',
    });
  },
  // 登录返回加密 key 和 token
  'POST /Api/Auth/verify': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        h: '18c1282e',
        n: 'fafe8a38',
        e: '83176cf4',
        token: '314d4484',
        r: 'f288bd22',
      },
    });
  },
  // 下属驿站，品牌管理
  'POST /Api/YZ/CourierStation/getBrandsAuth': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        {
          brand: '品牌1',
          auth: -1,
        },
        {
          brand: '品牌2',
          auth: 13,
        },
      ],
    });
  },

  // 关闭订单
  'POST /Api/OrderData/closeOrder': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
    });
  },

  // 添加导出任务
  'POST /Api/OrderData/exportData': (req, res) => {
    res.send({ code: 0, msg: '成功', data: [] });
  },
  // 订单明细，分配订单
  'POST /Api/OrderData/allocationOrder': (req, res) => {
    res.send({ code: 0, msg: '成功', data: [] });
  },
  // 订单明细，获取快递品牌
  'POST /Api/OrderData/brands': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        {
          brand: 'sf',
          short_name: '顺丰',
        },
        {
          brand: 'ems',
          short_name: '邮政',
        },
      ],
    });
  },
  // 获取当前用户是否有设置百世巴枪密码的权限
  'POST /Api/HtWhitePhone/getHtWhitePhone': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: 0,
    });
  },

  // 每日数据统计，获取昨日总览数据
  'POST /Api/InnActiveStatictis/stat': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        in_num: 10,
        out_num: 20,
        back_num: 12,
        sms_num: 59,
        order_num: 52,
        fee_num: 21,
        station_num: 13,
        fans_num: 13,
        out_rate: 98.2,
        complaint_rate: 33,
        yz_in_num: 3,
        yz_out_num: 3,
        yz_out_rate: 98,
        total_station_num: 34,
        total_fans_num: 88,
        sms_fee: 15.77,
        split_num: 0.3,
        in_growth_num: 20,
        out_growth_num: -20,
      },
    });
  },
  // 出库折线
  'POST /Api/InnActiveStatictis/storageSummary': storageSummary,
  // 出库饼图
  'POST /Api/InnActiveStatictis/storagePic': storagePic,
  // 柱状图
  'POST /Api/InnActiveStatictis/rank': getHistogram,
  // 短信折线
  'POST /Api/InnActiveStatictis/smsSummary': smsSummary,
  // 订单折线
  'POST /Api/InnActiveStatictis/orderSummary': orderSummary,
  // 30日入库率
  'POST /Api/InnActiveStatictis/inStorageRate': inStorageRate,
  // 每周投诉率
  'POST /Api/InnActiveStatictis/complainRate': eachWeekComplaintRate, // 数据统计，获取昨日总览数据
  /**
   * 获取对账单导出界面信息
   *  */
  'POST /Api/OrderData/getExportInfo': getExportInfo,
  /**
   * 导出对账单，添加任务
   *  */
  'POST /Api/OrderData/addTask': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: 0,
    });
  },
  'POST /Api/InnStatictis/getDakInList': (req, res) => {
    res.send({
      code: 0,
      msg: '',
      data: {
        page: '1', // 当前页
        pageSize: '5', // 每页查询数
        list: [
          {
            num: '0', // 入库数
            city_id: '2225252', // [驿站ID|分公司ID(中邮驿站)]
            name: '25驿站', // 驿站名称或中邮分公司名称
          },
          {
            num: '0',
            city_id: '2225897',
            name: '楼下驿站',
          },
        ],
        total: '2', // 总数量
      },
    });
  },
  'POST /Api/ShareLink/getOpenVideoDakList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        total: 2,
        list: [
          {
            cm_id: '1428308', // 驿站ID
            name: '捷克测试驿站', // 驿站名称
            concat_phone: '13411112222', // 驿站手机
          },
          {
            cm_id: '14283081', // 驿站ID
            name: '捷克测试驿站1', // 驿站名称
            concat_phone: '134111122221', // 驿站手机
          },
        ],
        page: '1',
        pageSize: '1',
      },
    });
  },
  'POST /Api/YZ/CourierStation/getLeagueStockFailList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        total: 2,
        list: [
          {
            inn_name: '我是驿站名',
            inn_phone: '13411112222',
            in_fail_num: 10,
            out_fail_num: 20,
            back_fail_num: 10,
            cm_id: 1,
            created_time: '2021-02-08 17:21:23',
            complate_auth: 1,
          },
          {
            inn_name: '我是驿站名',
            inn_phone: '13411112222',
            in_fail_num: 10,
            out_fail_num: 20,
            back_fail_num: 10,
            cm_id: 12,
            created_time: '2021-02-08 17:21:23',
          },
        ],
        page: '1',
        pageSize: '1',
      },
    });
  },
  'POST /Api/YZ/CourierStation/reupload': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
    });
  },
  'POST /Api/InnStatictis/dakOrEcIncomeByDay': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        {
          day: '2025-02-05',
          cnt: '0.00',
        },
        {
          day: '2025-02-06',
          cnt: '0.00',
        },
      ],
    });
  },
  'POST /v1/TotalDistribution/GunInformation/getLoginStatus': (req, res) => {
    res.send({
      code: req.body.brand == 'yt' ? 10031 : 0,
      msg: '成功',
      data: [
        {
          brand: 'zt',
          type: '3',
          errcode: 10000,
          errmsg: '中通pda账号掉线，请重新登录',
        },
      ],
    });
  },
  'POST /Api/OrderData/getDefaultOrderUser': (req, res) => {
    res.send(
      mock({
        code: 0,
        'data|1': [
          { shop_id: 7, phone: 13524777545, kb_type: 's', create_time: '2025-05-14 14:24:19' },
          [],
        ],
      }),
    );
  },
  'POST /Api/OrderData/delDefaultOrderUser': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
    });
  },
  'POST /Api/OrderData/setDefaultOrderUser': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
    });
  },
  'POST /Api/YZ/CourierStation/setEcForbidDeliver': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: '',
    });
  },
  'POST /v1/TotalDistribution/GunInformation/getEmsOrgCodeList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        {
          org_id: '23123',
          org_no: '1232144',
        },
        {
          org_id: '31234',
          org_no: '4234324',
        },
      ],
    });
  },
  'POST /Api/YZ/CourierStation/checkStoreAuth': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: { auth: 1 },
    });
  },
};

// 代理
// export default {
// 'POST /api/(.*)': 'http://city.kuaidihelp.com/api/',
// };
// format 函数用于保证本地的模拟正常，如果写了文档，这个函数转换是必要的
export default proxy;
// export default (noProxy ? {} : delay(proxy, 1000));
